// 直接测试 franc-min 的检测效果
import { franc } from './echo-lab/node_modules/franc-min/index.js';

// 测试用例
const testCases = [
  "Hi, I'd like a medium latte, please.",
  "Hmm… I'll take a blueberry muffin too.",
  "Got it. That'll be $6.75.",
  "Hello world!",
  "こんにちは",
  "你好世界",
  "今天天气很好。",
];

console.log("franc-min 语言检测测试结果:");
console.log("=".repeat(50));

testCases.forEach((text, index) => {
  try {
    const result = franc(text);
    const isEnglish = result === 'eng' ? '✅ 英语' : '';
    const status = text.includes("Hmm…") && result === 'eng' ? '🎉 问题已修复!' : '';
    console.log(`${index + 1}. "${text}" -> ${result} ${isEnglish} ${status}`);
  } catch (error) {
    console.log(`${index + 1}. "${text}" -> ERROR: ${error.message}`);
  }
});

console.log("\n" + "=".repeat(50));
console.log("测试完成！");
