// 测试修复后的语言检测功能
import { detectLanguage } from './echo-lab/src/utils/languageDetection.js';

// 测试用例
const testCases = [
  // 您提供的问题文本
  "Hi, I'd like a medium latte, please.",
  "Hmm… I'll take a blueberry muffin too.",
  "Got it. That'll be $6.75.",
  
  // 其他英语测试
  "Hello world!",
  "This is a test.",
  "Price: $19.99",
  
  // 日语测试（应该保持不变）
  "こんにちは",
  "今日は良い天気ですね。",
  
  // 中文测试（应该保持不变）
  "你好世界",
  "今天天气很好。",
  
  // 边界情况
  "",
  "   ",
  "123456",
  "😊🎉",
];

console.log("语言检测测试结果:");
console.log("=".repeat(50));

testCases.forEach((text, index) => {
  try {
    const result = detectLanguage(text);
    const status = text.includes("Hmm…") && result === "en" ? "✅ 修复成功" : "";
    console.log(`${index + 1}. "${text}" -> ${result} ${status}`);
  } catch (error) {
    console.log(`${index + 1}. "${text}" -> ERROR: ${error.message}`);
  }
});

console.log("\n" + "=".repeat(50));
console.log("测试完成！");
