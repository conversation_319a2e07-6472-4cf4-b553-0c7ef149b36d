# Echo Lab Nginx配置模板
# 将此文件复制到/etc/nginx/conf.d/echolab.conf并根据实际情况修改

# 全局设置
client_max_body_size 100M; # 允许上传最大100MB的文件

# 代理头哈希表设置 - 解决哈希表警告
proxy_headers_hash_max_size 1024; # 默认值为512，增加到1024
proxy_headers_hash_bucket_size 128; # 默认值为64，增加到128

# 限制请求频率 - 防止暴力破解和DoS攻击
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=auth_limit:10m rate=5r/m;
limit_conn_zone $binary_remote_addr zone=conn_limit:10m;

# HTTP服务器 - 重定向到HTTPS
server {
    if ($host = echolab.club) {
        return 301 https://$host$request_uri;
    } # managed by Certbot

    listen 80;
    server_name echolab.club www.echolab.club;

    # 将HTTP请求重定向到HTTPS
    return 301 https://$host$request_uri;
}

# HTTPS服务器 - www重定向到非www
server {
    listen 443 ssl;
    server_name www.echolab.club;

    # SSL证书配置
    ssl_certificate /etc/letsencrypt/live/echolab.club/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/echolab.club/privkey.pem;

    # 重定向www到非www
    return 301 https://echolab.club$request_uri;
}

# HTTPS服务器 - 主域名
server {
    listen 443 ssl;
    server_name echolab.club;

    # SSL证书配置 - 使用Let's Encrypt自动生成的证书
    ssl_certificate /etc/letsencrypt/live/echolab.club/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/echolab.club/privkey.pem; # managed by Certbot
    ssl_session_timeout 5m;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_prefer_server_ciphers on;

    # 日志配置
    access_log /var/log/nginx/echolab.access.log;
    error_log /var/log/nginx/echolab.error.log;

    # 前端静态文件
    location / {
        root /var/www/echo-lab; # 前端构建目录
        index index.html index.htm;
        try_files $uri $uri/ /index.html; # 支持前端路由
    }

    # 视频API - 长超时设置，支持大文件上传（必须在普通API之前）
    location ~ ^/api/video/ {
        # 应用视频相关的请求频率限制
        limit_req zone=api_limit burst=10 nodelay;
        limit_conn conn_limit 10;

        # 代理设置
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Request-ID $request_id;
        proxy_cache_bypass $http_upgrade;

        # 视频处理专用超时设置 - 15分钟
        proxy_connect_timeout 900s;     # 连接超时时间
        proxy_send_timeout 900s;        # 发送超时时间
        proxy_read_timeout 900s;        # 读取超时时间
        send_timeout 900s;              # 发送响应超时时间

        # 大文件支持
        client_max_body_size 100M;     # 允许上传最大100MB的文件

        # 安全头部
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    }

    # 认证API - 更严格的限制
    location ~ ^/api/auth/ {
        # 应用更严格的请求频率限制
        limit_req zone=auth_limit burst=5 nodelay;
        limit_conn conn_limit 5;

        # 代理设置
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Request-ID $request_id; # 添加请求ID
        proxy_cache_bypass $http_upgrade;

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 安全头部
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    }

    # 普通API代理 - 放在最后，作为兜底匹配
    location /api {
        # 应用请求频率限制 - 普通API请求
        limit_req zone=api_limit burst=20 nodelay;
        limit_conn conn_limit 20;

        # 代理设置
        proxy_pass http://localhost:3000; # 后端服务地址
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Request-ID $request_id; # 添加请求ID
        proxy_cache_bypass $http_upgrade;

        # 普通API超时设置 - 5分钟
        proxy_connect_timeout 300s;     # 连接超时时间
        proxy_send_timeout 300s;        # 发送超时时间
        proxy_read_timeout 300s;        # 读取超时时间
        send_timeout 300s;              # 发送响应超时时间

        # 增加客户端请求体大小限制
        client_max_body_size 100M;     # 允许上传最大100MB的文件

        # 安全头部
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    }

    # Google Analytics 代理已移除，直接使用 Google 服务

    # 倍速音频处理 - 在 OSS 代理之前添加
    location ~ ^/oss-resources/(?<file_path>audio/.+\.(mp3|wav|m4a))\?speed=(?<speed_value>[0-9.]+)$ {
        # 直接转发给后端处理，后端负责缓存检查
        proxy_pass http://localhost:3000/api/audio/speed?path=$file_path&speed=$speed_value;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

        # 支持流式传输
        proxy_buffering off;
        proxy_request_buffering off;

        # 音频处理需要较长时间
        proxy_connect_timeout 60s;
        proxy_read_timeout 120s;
        proxy_send_timeout 120s;

        # 缓存控制
        add_header Cache-Control "public, max-age=2592000"; # 30天缓存
        add_header Access-Control-Allow-Origin '*';
        add_header Access-Control-Allow-Methods 'GET, OPTIONS';

        # 支持范围请求（对音频很重要）
        proxy_force_ranges on;
    }

    # 阿里云OSS代理配置
    location /oss-resources/ {
        # 移除前缀，只保留路径的后半部分
        rewrite ^/oss-resources/(.*) /$1 break;

        # 代理到阿里云OSS
        proxy_pass https://echolab.oss-cn-hongkong.aliyuncs.com/;

        # 缓存设置
        proxy_cache_valid 200 302 304 7d;  # 缓存成功的响应7天
        proxy_cache_valid 404 1m;          # 缓存404响应1分钟

        # 代理头信息
        proxy_set_header Host echolab.oss-cn-hongkong.aliyuncs.com;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

        # CORS设置
        add_header Access-Control-Allow-Origin '*';
        add_header Access-Control-Allow-Methods 'GET, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';

        # 处理OPTIONS请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin '*';
            add_header Access-Control-Allow-Methods 'GET, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type 'text/plain charset=UTF-8';
            add_header Content-Length 0;
            return 204;
        }

        # 缓存控制
        add_header Cache-Control "public, max-age=604800"; # 7天缓存

        # 支持范围请求（对音频和视频很重要）
        proxy_force_ranges on;

        # 大文件支持 - 5分钟超时（OSS文件访问）
        client_max_body_size 100M;
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;

        # 启用压缩
        gzip on;
        gzip_types application/javascript text/css image/svg+xml;
        gzip_min_length 1000;
    }

    # 错误页面
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }

    # 安全相关配置
    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 禁止访问敏感目录
    location ~ ^/(config|logs|migrations|models|node_modules|scripts|services|temp)/ {
        deny all;
        return 404;
    }

    # 禁止访问敏感文件
    location ~ \.(env|sql|md|log|sh|conf|json|yml|yaml)$ {
        deny all;
        return 404;
    }

    # 限制请求方法
    if ($request_method !~ ^(GET|POST|PUT|DELETE|OPTIONS)$) {
        return 405;
    }

    # 全局安全头部
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    # CSP配置 - 支持Google Analytics和百度统计
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' data: https://www.googletagmanager.com https://hm.baidu.com; style-src 'self' 'unsafe-inline' data:; img-src * data: blob:; connect-src 'self' https: wss: blob: data:; font-src 'self' data:; object-src 'none'; media-src * blob: data:; frame-src 'self' https://www.googletagmanager.com; worker-src 'self' blob: data:; child-src 'self' blob:;" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

    # 启用gzip压缩
    gzip on;
    gzip_comp_level 6;
    gzip_min_length 1100;
    gzip_buffers 16 8k;
    gzip_proxied any;
    gzip_types
        text/plain
        text/css
        text/js
        text/xml
        text/javascript
        application/javascript
        application/json
        application/xml
        application/rss+xml
        image/svg+xml;

    # 自定义错误页面
    error_page 400 401 403 404 405 429 /error.html;
    error_page 500 502 503 504 /50x.html;
}
