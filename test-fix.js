// 测试修复后的语言检测
function detectLanguage(text) {
  if (!text || typeof text !== "string" || text.trim() === "") return "auto";

  // 日语检测（假名字符）
  if (/[\u3040-\u30FF\u31F0-\u31FF\uFF65-\uFF9F]/.test(text)) {
    return "ja";
  }

  // 中文检测（汉字字符）
  if (/[\u4e00-\u9fff]/.test(text)) {
    return "zh-CN"; // 简化处理
  }

  // 英文检测（ASCII字符 + 常用Unicode标点符号）
  if (
    /^[\u0000-\u007F\u2026\u2018\u2019\u201C\u201D\s\.,'"\-!?$]+$/.test(text)
  ) {
    return "en";
  }

  return "unknown";
}

// 测试您提供的文本
const testTexts = [
  "Hi, I'd like a medium latte, please.",
  "Hmm… I'll take a blueberry muffin too.",
  "Got it. That'll be $6.75.",
];

console.log("修复后的语言检测结果:");
testTexts.forEach((text) => {
  const result = detectLanguage(text);
  console.log(`"${text}" -> ${result}`);
});

// 额外测试用例
const additionalTests = [
  'Hello "world"!', // 智能引号
  "Wait… what?", // 省略号
  'It\'s "amazing"!', // 混合引号
  "Price: $19.99", // 美元符号
  "Test with emoji 😊", // 表情符号（应该仍然是unknown）
];

console.log("\n额外测试用例:");
additionalTests.forEach((text) => {
  const result = detectLanguage(text);
  console.log(`"${text}" -> ${result}`);
});
