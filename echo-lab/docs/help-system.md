# 帮助系统使用指南

Echo Lab 的帮助系统提供了一个统一、可扩展的用户帮助功能，支持多页面、多章节的帮助内容展示。

## 系统架构

### 核心组件

1. **HelpDialog** (`src/components/help/HelpDialog.vue`)
   - 通用帮助对话框组件
   - 支持多章节导航
   - 支持可展开的详细内容
   - 响应式设计，适配桌面和移动端

2. **HelpButton** (`src/components/help/HelpButton.vue`)
   - 帮助按钮组件
   - 支持多种样式（圆形、文字、链接等）
   - 可集成到任何页面

3. **GlobalHelpProvider** (`src/components/help/GlobalHelpProvider.vue`)
   - 全局帮助提供者
   - 在应用根部提供帮助对话框
   - 管理全局帮助状态

4. **FloatingHelpButton** (`src/components/help/FloatingHelpButton.vue`)
   - 浮动帮助按钮组件
   - 只在播放页显示，固定在页面右侧
   - 专门用于播放页的使用帮助

### 配置和服务

5. **helpContent.js** (`src/config/helpContent.js`)
   - 帮助内容配置文件
   - 集中管理各页面的帮助内容
   - 支持结构化的帮助内容定义

6. **useHelp.js** (`src/composables/useHelp.js`)
   - 帮助功能组合式函数
   - 提供统一的帮助状态管理
   - 支持编程式调用

## 使用方法

### 1. 播放页浮动帮助按钮

系统在播放页集成了专用的浮动帮助按钮：

- 只在播放页显示，固定在页面右侧
- 专门用于播放页复杂功能的使用指导
- 其他页面的帮助通过首页帮助入口访问

### 2. 手动添加帮助按钮

在特定组件中使用 HelpButton：

```vue
<template>
  <HelpButton page-type="player" type="primary" />
</template>

<script setup>
import HelpButton from '@/components/help/HelpButton.vue';
</script>
```

### 3. 编程式调用

使用 useHelp 组合式函数：

```vue
<script setup>
import { useHelp } from '@/composables/useHelp';

const { showHelp, hideHelp } = useHelp();

const showPlayerHelp = () => {
  showHelp('player');
};
</script>
```

### 4. 配置帮助内容

在 `src/config/helpContent.js` 中添加新的帮助配置：

```javascript
export const NEW_PAGE_HELP_CONFIG = {
  title: '新页面使用指南',
  sections: [
    {
      id: 'basic',
      title: '基础功能',
      description: '了解页面的基本功能',
      items: [
        {
          id: 'feature1',
          title: '功能1',
          description: '功能1的简要说明',
          details: [
            {
              id: 'detail1',
              title: '详细说明',
              content: '功能的详细使用方法',
              steps: [
                '步骤1：...',
                '步骤2：...'
              ],
              tips: [
                '提示1：...',
                '提示2：...'
              ]
            }
          ]
        }
      ]
    }
  ]
};
```

## 帮助功能内容

### 播放页专用帮助
播放页的浮动帮助按钮提供专门的播放功能指南：
- 基础操作：播放控制、内容导航、显示设置
- 环节配置：环节概述和管理、重复设置和自定义参数、拖拽排序功能
- 高级功能：翻译功能配置、关键词功能设置

### 首页综合帮助
首页的"使用帮助"菜单项提供 Echo Lab 已开放功能的综合指南：
- **首页功能**：内容浏览、用户功能等
- **播放页功能**：完整的播放页功能说明（与专用帮助内容相同）

注意：编辑器功能和视频导出功能目前未对外开放，因此不在帮助内容中展示。

## 扩展指南

### 为新页面添加帮助

1. 在 `helpContent.js` 中定义帮助配置
2. 在页面中添加 HelpButton 组件
3. 在 HELP_CONFIG_MAP 中注册新配置

### 自定义帮助样式

HelpButton 支持多种自定义选项：

```vue
<HelpButton
  page-type="player"
  :circle="true"
  size="small"
  type="info"
  :show-text="false"
  custom-class="my-help-button"
  tooltip="自定义提示文字"
/>
```

### 添加新的帮助内容类型

帮助内容支持以下类型：
- 基本文本内容
- 步骤列表 (steps)
- 提示列表 (tips)
- 可展开的详细内容

## 测试

- 在播放页点击右侧浮动的"帮助"按钮查看播放页专用帮助
- 在首页侧边栏点击"使用帮助"菜单项查看 Echo Lab 已开放功能的综合帮助
- 综合帮助包含首页功能和播放页功能的完整使用指南

## 移动端优化

### 抽屉式交互设计
为了提供更符合移动端习惯的交互体验，帮助功能在移动端使用抽屉组件：

```javascript
// 移动设备检测
const isMobile = ref(isMobileDevice());
```

```vue
<!-- 桌面端对话框 -->
<StandardDialog v-if="!isMobile" v-model="dialogVisible"
  :title="helpConfig.title" width="80%" height="70vh">
  <HelpContent ... />
</StandardDialog>

<!-- 移动端抽屉 -->
<el-drawer v-else v-model="dialogVisible"
  :title="helpConfig.title" direction="btt" size="85%">
  <HelpContent ... />
</el-drawer>
```

### 优化特性
- **设备检测**：使用 `isMobileDevice()` 函数检测设备类型
- **抽屉交互**：移动端使用底部向上滑出的抽屉组件
- **85% 高度**：既充分利用空间，又保留操作便利性
- **组件复用**：桌面端和移动端共享 `HelpContent` 组件
- **原生体验**：符合移动应用的标准交互模式
- **手势友好**：支持下拉关闭等原生手势操作
- **触摸友好**：确保所有交互元素有足够的触摸区域

## 注意事项

1. 帮助内容应该简洁明了，避免过于复杂的嵌套结构
2. 移动端采用全屏显示，充分利用屏幕空间
3. 播放页浮动帮助按钮固定在页面右侧，专门用于复杂功能的帮助
4. 帮助内容应该与实际功能保持同步更新
