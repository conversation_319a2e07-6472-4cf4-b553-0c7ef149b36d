{"name": "echo-lab", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build --mode production", "build:analyze": "ANALYZE=true vite build --mode production", "preview": "vite preview"}, "dependencies": {"@breezystack/lamejs": "1.2.7", "axios": "1.5.0", "cropperjs": "1.6.2", "crypto-js": "4.2.0", "dom-to-image-more": "3.6.0", "element-plus": "2.9.10", "franc-min": "^6.2.0", "howler": "2.2.4", "html2canvas": "1.4.1", "jszip": "3.10.1", "langdetect": "0.2.1", "mobile-detect": "1.4.5", "mp4-muxer": "5.2.1", "pinia": "2.1.4", "uuid": "9.0.0", "vue": "3.3.4", "vue-advanced-cropper": "2.8.9", "vue-router": "4.2.4", "vuedraggable": "4.1.0", "wavesurfer.js": "7.9.5"}, "devDependencies": {"@iconify-json/ep": "1.2.2", "@vitejs/plugin-vue": "5.2.4", "rollup-plugin-visualizer": "6.0.1", "sass": "1.88.0", "terser": "5.39.0", "unplugin-auto-import": "19.2.0", "unplugin-icons": "22.1.0", "unplugin-vue-components": "28.5.0", "vite": "6.3.5", "vite-plugin-pwa": "1.0.0"}}