/**
 * 语速配置
 * 定义不同语言和级别的语速预设
 */

// 日语级别语速预设
export const JAPANESE_LEVEL_RATES = [
  { value: 0.7, label: "N5 (0.7x) - 初级" },
  { value: 0.8, label: "N4 (0.8x) - 初中级" },
  { value: 0.9, label: "N3 (0.9x) - 中级" },
  { value: 1.0, label: "N2 (1.0x) - 中高级" },
  { value: 1.2, label: "N1 (1.2x) - 高级" },
];

// 语言默认语速
export const DEFAULT_LANGUAGE_RATES = {
  ja: 1.0, // 日语默认 1.0
  "zh-CN": 1.0, // 中文默认 1.0
  "zh-TW": 1.0, // 繁体中文默认 1.0
  en: 1.0, // 英语默认 1.0
};

/**
 * 获取语言的默认语速
 * @param {string} langCode 语言代码
 * @returns {number} 默认语速
 */
export function getDefaultRateForLanguage(langCode) {
  return DEFAULT_LANGUAGE_RATES[langCode] || 1.0;
}

/**
 * 获取日语级别对应的语速
 * @param {string} level 日语级别 (N1-N5)
 * @returns {number} 对应的语速
 */
export function getRateForJapaneseLevel(level) {
  switch (level) {
    case "N5":
      return 0.7;
    case "N4":
      return 0.8;
    case "N3":
      return 0.9;
    case "N2":
      return 1.0;
    case "N1":
      return 1.2;
    default:
      return 1.0;
  }
}

export default {
  JAPANESE_LEVEL_RATES,
  DEFAULT_LANGUAGE_RATES,
  getDefaultRateForLanguage,
  getRateForJapaneseLevel,
};
