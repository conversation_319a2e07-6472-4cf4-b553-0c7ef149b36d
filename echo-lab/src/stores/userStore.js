/**
 * 用户状态存储
 * 管理用户登录状态和信息
 */
import { defineStore } from "pinia";
import {
  isLoggedIn,
  getUser,
  getCurrentUser,
  updateUser as updateUserApi,
  logout as logoutApi,
} from "@/services/authService";
import { getUserLevelInfo, getUserFeatures } from "@/services/userLevelService";

export const useUserStore = defineStore("user", {
  state: () => ({
    user: getUser(),
    isLoggedIn: isLoggedIn(),
    loading: false,
    error: null,
    userLevel: undefined, // 用户等级
    levelName: "", // 等级名称
    levelFeatures: [], // 用户可用功能
    subscription: null, // 订阅信息
  }),

  getters: {
    // 用户名或邮箱前缀
    displayName: (state) => {
      if (!state.user) return "";
      return state.user.username || state.user.email.split("@")[0];
    },

    // 用户角色
    isAdmin: (state) => {
      if (!state.user) return false;
      return state.user.role === "admin";
    },

    // 用户设置
    settings: (state) => {
      if (!state.user) return {};
      return state.user.settings || {};
    },

    // 用户等级名称
    userLevelName: (state) => {
      return (
        state.levelName ||
        (state.userLevel !== undefined ? `等级 ${state.userLevel}` : "")
      );
    },

    // 检查用户是否有特定功能权限
    hasFeature: (state) => (featureKey) => {
      if (!state.isLoggedIn) return false;
      if (state.user && state.user.role === "admin") return true;
      return state.levelFeatures.includes(featureKey);
    },

    // 检查用户是否达到特定等级
    hasLevel: (state) => (level) => {
      if (!state.isLoggedIn) return false;
      if (state.user && state.user.role === "admin") return true;
      return state.userLevel !== undefined && state.userLevel >= level;
    },

    // 是否有活跃订阅
    hasActiveSubscription: (state) => {
      return !!state.subscription && state.subscription.status === "active";
    },
  },

  actions: {
    /**
     * 获取当前用户信息
     */
    async fetchCurrentUser() {
      if (!isLoggedIn()) {
        this.user = null;
        this.isLoggedIn = false;
        return;
      }

      this.loading = true;
      this.error = null;

      try {
        const response = await getCurrentUser();
        if (response.success) {
          this.user = response.user;
          this.isLoggedIn = true;

          // 获取用户等级信息
          await this.fetchUserLevel();

          // 获取用户功能权限
          await this.fetchUserFeatures();
        } else {
          this.user = null;
          this.isLoggedIn = false;
          this.error = response.error;
        }
      } catch (error) {
        this.user = null;
        this.isLoggedIn = false;
        this.error = error.error || "获取用户信息失败";
      } finally {
        this.loading = false;
      }
    },

    /**
     * 获取用户等级信息
     */
    async fetchUserLevel() {
      if (!this.isLoggedIn) return;

      try {
        const levelInfo = await getUserLevelInfo();
        this.userLevel = levelInfo.level;
        this.levelName = levelInfo.levelName;
        this.subscription = levelInfo.subscription;
      } catch (error) {
        console.error("获取用户等级信息失败:", error);
      }
    },

    /**
     * 获取用户功能权限
     */
    async fetchUserFeatures() {
      if (!this.isLoggedIn) return;

      try {
        const features = await getUserFeatures();
        this.levelFeatures = features;
      } catch (error) {
        console.error("获取用户功能权限失败:", error);
      }
    },

    /**
     * 更新用户信息
     * @param {Object} userData 用户数据
     */
    async updateUser(userData) {
      if (!isLoggedIn()) {
        return { success: false, error: "未登录" };
      }

      this.loading = true;
      this.error = null;

      try {
        const response = await updateUserApi(userData);
        if (response.success) {
          this.user = response.user;
          return { success: true };
        } else {
          this.error = response.error;
          return { success: false, error: response.error };
        }
      } catch (error) {
        this.error = error.error || "更新用户信息失败";
        return { success: false, error: this.error };
      } finally {
        this.loading = false;
      }
    },

    /**
     * 退出登录
     */
    logout() {
      logoutApi();
      this.user = null;
      this.isLoggedIn = false;
      this.userLevel = undefined;
      this.levelName = "";
      this.levelFeatures = [];
      this.subscription = null;
    },

    /**
     * 设置用户信息（登录成功后调用）
     * @param {Object} user 用户信息
     */
    setUser(user) {
      this.user = user;
      this.isLoggedIn = true;

      // 获取用户等级信息和功能权限
      this.fetchUserLevel();
      this.fetchUserFeatures();
    },

    /**
     * 设置用户等级
     * @param {number} level 用户等级
     * @param {string} levelName 等级名称
     */
    setUserLevel(level, levelName = "") {
      this.userLevel = level;
      if (levelName) {
        this.levelName = levelName;
      }
    },

    /**
     * 设置用户功能权限
     * @param {Array<string>} features 功能标识符列表
     */
    setUserFeatures(features) {
      this.levelFeatures = features;
    },
  },
});
