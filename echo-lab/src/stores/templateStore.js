/**
 * 播放策略模板状态管理
 * 使用Pinia管理模板状态
 */
import { defineStore } from "pinia";
import templateService from "@/services/templateService";
import { ElMessage } from "element-plus";

export const useTemplateStore = defineStore("template", {
  state: () => ({
    // 模板列表
    systemTemplates: [],
    userTemplates: [],
    publicTemplates: [],

    // 当前选中的模板
    currentTemplate: null,

    // 加载状态
    loading: false,
    error: null,
  }),

  getters: {
    /**
     * 所有模板列表
     */
    allTemplates: (state) => {
      // 确保所有数组都已初始化
      const systemTemplates = Array.isArray(state.systemTemplates)
        ? state.systemTemplates
        : [];
      const userTemplates = Array.isArray(state.userTemplates)
        ? state.userTemplates
        : [];
      const publicTemplates = Array.isArray(state.publicTemplates)
        ? state.publicTemplates
        : [];

      return [...systemTemplates, ...userTemplates, ...publicTemplates];
    },

    /**
     * 根据ID查找模板
     */
    templateById: (state) => (id) => {
      if (!id) return null;

      // 确保所有数组都已初始化
      const systemTemplates = Array.isArray(state.systemTemplates)
        ? state.systemTemplates
        : [];
      const userTemplates = Array.isArray(state.userTemplates)
        ? state.userTemplates
        : [];
      const publicTemplates = Array.isArray(state.publicTemplates)
        ? state.publicTemplates
        : [];

      const allTemplates = [
        ...systemTemplates,
        ...userTemplates,
        ...publicTemplates,
      ];
      return (
        allTemplates.find((template) => template && template.id === id) || null
      );
    },

    /**
     * 是否有用户模板
     */
    hasUserTemplates: (state) => state.userTemplates.length > 0,

    /**
     * 是否有公开模板
     */
    hasPublicTemplates: (state) => state.publicTemplates.length > 0,
  },

  actions: {
    /**
     * 加载模板列表
     * @param {Object} params 查询参数
     */
    async loadTemplates(params = {}) {
      this.loading = true;
      this.error = null;

      try {
        const response = await templateService.getTemplates(params);

        if (response.success) {
          this.systemTemplates = response.data.system || [];
          this.userTemplates = response.data.user || [];
          this.publicTemplates = response.data.public || [];
        } else {
          throw new Error(response.error || "获取模板列表失败");
        }
      } catch (error) {
        this.error = error.message;
        ElMessage.error(`获取模板列表失败: ${error.message}`);
        console.error("加载模板列表失败:", error);
      } finally {
        this.loading = false;
      }
    },

    /**
     * 根据ID获取模板
     * @param {string} id 模板ID
     * @returns {Object|null} 模板对象
     */
    async getTemplateById(id) {
      try {
        // 先从本地状态查找
        let template = null;
        try {
          template = this.templateById(id);
        } catch (templateError) {
          console.warn("从本地查找模板失败:", templateError);
          // 继续从服务器获取
        }

        if (!template) {
          // 本地没有，从服务器获取
          const response = await templateService.getTemplateById(id);
          if (response.success) {
            template = response.data;
          }
        }

        return template;
      } catch (error) {
        console.error("获取模板失败:", error);
        ElMessage.error(`获取模板失败: ${error.message}`);
        return null;
      }
    },

    /**
     * 创建模板
     * @param {Object} templateData 模板数据
     * @returns {Object|null} 创建的模板
     */
    async createTemplate(templateData) {
      try {
        // 验证配置格式
        const validation = templateService.validateTemplateConfig(
          templateData.config
        );

        if (!validation.valid) {
          throw new Error(`配置格式错误: ${validation.errors.join(", ")}`);
        }

        const response = await templateService.createTemplate(templateData);

        if (response && response.success) {
          // 添加到用户模板列表
          this.userTemplates.unshift(response.data);
          ElMessage.success("模板创建成功");
          return response.data;
        } else {
          throw new Error(response?.error || "创建模板失败");
        }
      } catch (error) {
        console.error("创建模板完整错误信息:", error);
        console.error("错误堆栈:", error.stack);
        if (error.response) {
          console.error(
            "HTTP响应错误:",
            error.response.status,
            error.response.data
          );
        }
        ElMessage.error(`创建模板失败: ${error.message}`);
        return null;
      }
    },

    /**
     * 更新模板
     * @param {string} id 模板ID
     * @param {Object} updateData 更新数据
     * @returns {Object|null} 更新后的模板
     */
    async updateTemplate(id, updateData) {
      try {
        // 验证配置格式（如果有更新配置）
        if (updateData.config) {
          const validation = templateService.validateTemplateConfig(
            updateData.config
          );
          if (!validation.valid) {
            throw new Error(`配置格式错误: ${validation.errors.join(", ")}`);
          }
        }

        const response = await templateService.updateTemplate(id, updateData);

        if (response && response.success) {
          // 更新本地状态
          const index = this.userTemplates.findIndex(
            (template) => template.id === id
          );
          if (index !== -1) {
            this.userTemplates[index] = response.data;
          }

          // 如果是当前选中的模板，也要更新
          if (this.currentTemplate && this.currentTemplate.id === id) {
            this.currentTemplate = response.data;
          }

          ElMessage.success("模板更新成功");
          return response.data;
        } else {
          throw new Error(response?.error || "更新模板失败");
        }
      } catch (error) {
        ElMessage.error(`更新模板失败: ${error.message}`);
        console.error("更新模板失败:", error);
        return null;
      }
    },

    /**
     * 删除模板
     * @param {string} id 模板ID
     * @returns {boolean} 是否删除成功
     */
    async deleteTemplate(id) {
      try {
        await templateService.deleteTemplate(id);

        // 从本地状态移除
        this.userTemplates = this.userTemplates.filter(
          (template) => template.id !== id
        );

        // 如果删除的是当前选中的模板，清除选中状态
        if (this.currentTemplate && this.currentTemplate.id === id) {
          this.currentTemplate = null;
        }

        ElMessage.success("模板删除成功");
        return true;
      } catch (error) {
        ElMessage.error(`删除模板失败: ${error.message}`);
        console.error("删除模板失败:", error);
        return false;
      }
    },

    /**
     * 使用模板
     * @param {string} id 模板ID
     * @returns {boolean} 是否成功
     */
    async useTemplate(id) {
      try {
        await templateService.useTemplate(id);

        // 更新本地使用次数
        try {
          const template = this.templateById(id);
          if (template && template.type === "user") {
            template.usageCount = (template.usageCount || 0) + 1;
          }
        } catch (templateError) {
          console.warn("更新本地模板使用次数失败:", templateError);
          // 不影响主要功能，继续执行
        }

        return true;
      } catch (error) {
        console.error("更新使用记录失败:", error);
        return false;
      }
    },

    /**
     * 复制模板
     * @param {string} id 原模板ID
     * @param {string} newName 新模板名称
     * @returns {Object|null} 新模板
     */
    async duplicateTemplate(id, newName) {
      try {
        const response = await templateService.duplicateTemplate(id, newName);

        if (response && response.success) {
          // 添加到用户模板列表
          this.userTemplates.unshift(response.data);
          ElMessage.success("模板复制成功");
          return response.data;
        } else {
          throw new Error(response?.error || "复制模板失败");
        }
      } catch (error) {
        ElMessage.error(`复制模板失败: ${error.message}`);
        console.error("复制模板失败:", error);
        return null;
      }
    },

    /**
     * 选择模板
     * @param {Object} template 模板对象
     */
    selectTemplate(template) {
      this.currentTemplate = template;
    },

    /**
     * 清除选中的模板
     */
    clearSelectedTemplate() {
      this.currentTemplate = null;
    },

    /**
     * 应用模板到内容配置
     * @param {Object} serverConfig 服务器内容配置
     * @param {Object} template 模板对象
     * @returns {Object} 应用模板后的配置
     */
    applyTemplateToContent(serverConfig, template) {
      try {
        return templateService.applyTemplateToContent(serverConfig, template);
      } catch (error) {
        ElMessage.error(`应用模板失败: ${error.message}`);
        console.error("应用模板失败:", error);
        return serverConfig;
      }
    },

    /**
     * 从当前配置创建模板
     * @param {Object} currentConfig 当前播放配置
     * @returns {Object} 模板配置
     */
    createTemplateFromConfig(currentConfig) {
      try {
        return templateService.createTemplateFromConfig(currentConfig);
      } catch (error) {
        ElMessage.error(`创建模板配置失败: ${error.message}`);
        console.error("创建模板配置失败:", error);
        return null;
      }
    },

    /**
     * 重置状态
     */
    resetState() {
      this.systemTemplates = [];
      this.userTemplates = [];
      this.publicTemplates = [];
      this.currentTemplate = null;
      this.loading = false;
      this.error = null;
    },
  },
});
