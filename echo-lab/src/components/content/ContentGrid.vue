<!--
  内容网格组件
  用于在首页展示内容卡片的网格布局
-->
<template>
  <div class="content-grid" :class="{ 'is-loading': loading }">
    <!-- 加载状态 -->
    <div v-if="loading" class="grid-loading">
      <el-skeleton :rows="2" animated />
      <el-skeleton :rows="2" animated />
    </div>

    <!-- 内容为空 -->
    <div v-else-if="contents.length === 0" class="grid-empty">
      <el-empty :description="emptyText" :image-size="100">
        <template #description>
          <p>{{ emptyText }}</p>
        </template>
      </el-empty>
    </div>

    <!-- 内容网格 -->
    <div v-else class="grid-container">
      <PublicContentCard v-for="content in contents" :key="content.id" :content="content" class="grid-item">
      </PublicContentCard>
    </div>

    <!-- 分页 -->
    <div v-if="showPagination && !loading && contents.length > 0" class="grid-pagination">
      <el-pagination v-model="currentPage" :page-size="pageSize" :page-sizes="[12, 24, 36, 48]"
        :total="total" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import PublicContentCard from './PublicContentCard.vue';

const props = defineProps({
  contents: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  pagination: {
    type: Object,
    default: () => ({
      total: 0,
      page: 1,
      pageSize: 12
    })
  },
  showPagination: {
    type: Boolean,
    default: true
  },
  emptyText: {
    type: String,
    default: '暂无内容'
  }
});

const emit = defineEmits(['pagination-change']);

// 分页状态
const currentPage = ref(props.pagination.page);
const pageSize = ref(props.pagination.pageSize);
const total = computed(() => props.pagination.total);

// 监听分页变化
watch([currentPage, pageSize], () => {
  emit('pagination-change', {
    page: currentPage.value,
    pageSize: pageSize.value
  });
});

// 处理分页变化
const handleSizeChange = (val) => {
  pageSize.value = val;
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
};
</script>

<style scoped>
.content-grid {
  width: 100%;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(18rem, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.grid-item {
  height: 100%;
}

.grid-loading {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(18rem, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.grid-empty {
  padding: 3rem 0;
  text-align: center;
}

.grid-pagination {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

/* 响应式布局 - 使用类名而非媒体查询 */
.tablet-device .grid-container {
  grid-template-columns: repeat(auto-fill, minmax(15rem, 1fr));
  gap: 1rem;
}

.tablet-device .grid-loading {
  grid-template-columns: repeat(auto-fill, minmax(15rem, 1fr));
  gap: 1rem;
}

.mobile-device .grid-container {
  grid-template-columns: repeat(auto-fill, minmax(100%, 1fr));
  gap: 0.5rem;
}

.mobile-device .grid-loading {
  grid-template-columns: repeat(auto-fill, minmax(100%, 1fr));
  gap: 0.5rem;
}

.mobile-device .grid-pagination {
  margin-top: 1.5rem;
  width: 100%;
  overflow-x: auto;
}

.mobile-device .grid-pagination :deep(.el-pagination) {
  white-space: nowrap;
  width: max-content;
  min-width: 100%;
  justify-content: center;
}

.mobile-device .grid-pagination :deep(.el-pagination__sizes) {
  display: none;
}
</style>
