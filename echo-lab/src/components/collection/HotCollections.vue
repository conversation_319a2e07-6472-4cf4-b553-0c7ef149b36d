<!--
  全部合集组件
  列表式展示所有合集，支持分页加载
-->
<template>
  <div class="hot-collections">
    <div class="collections-grid" :class="{ 'mobile-layout': isMobile }">
      <!-- 加载状态 -->
      <div v-if="loading && collections.length === 0" class="grid-loading">
        <el-skeleton :rows="2" animated />
        <el-skeleton :rows="2" animated />
      </div>

      <!-- 内容为空 -->
      <div v-else-if="collections.length === 0" class="grid-empty">
        <el-empty description="暂无合集" :image-size="100" />
      </div>

      <!-- 合集列表 -->
      <div v-else class="grid-container">
        <CollectionCard 
          v-for="collection in collections" 
          :key="collection.id" 
          :collection="collection"
          :show-actions="false" 
          :show-author="true"
          :show-view-count="true" 
          :show-favorite-count="true" 
          :show-favorite-button="true"
          @toggle-favorite="$emit('toggle-favorite', collection)"
          class="grid-item"
        />
      </div>
    </div>

    <!-- 加载更多 -->
    <div v-if="hasMore" class="load-more-section">
      <el-button 
        type="primary" 
        size="large" 
        :loading="loading" 
        @click="$emit('load-more')"
        class="load-more-btn"
      >
        {{ loading ? '加载中...' : '加载更多' }}
      </el-button>
    </div>

    <!-- 统计信息 -->
    <div v-if="total > 0" class="content-stats">
      <span class="stats-text">
        已显示 {{ collections.length }} / {{ total }} 个合集
      </span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { isMobileDevice } from '@/utils/deviceDetector';
import CollectionCard from '@/components/collection/CollectionCard.vue';

const props = defineProps({
  collections: { type: Array, default: () => [] },
  loading: Boolean,
  total: { type: Number, default: 0 },
  hasMore: { type: Boolean, default: false }
});

defineEmits(['toggle-favorite', 'load-more']);

const isMobile = computed(() => isMobileDevice());
</script>

<style scoped>
.hot-collections {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.06);
  border: 0.0625rem solid #f0f2f5;
}

.collections-grid {
  width: 100%;
}

.grid-loading {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(20rem, 1fr));
  gap: 1.5rem;
  padding: 1rem 0;
}

.grid-empty {
  padding: 2rem 0;
  text-align: center;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(20rem, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.grid-item {
  width: 100%;
}

.load-more-section {
  text-align: center;
  padding: 1.5rem 0;
  border-top: 0.0625rem solid #f0f2f5;
}

.load-more-btn {
  min-width: 8rem;
}

.content-stats {
  text-align: center;
  padding: 1rem 0;
  color: #909399;
  font-size: 0.875rem;
  border-top: 0.0625rem solid #f0f2f5;
}

.stats-text {
  background-color: #f8f9fa;
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  display: inline-block;
}

/* 移动端优化 */
.mobile-layout .hot-collections {
  padding: 0.75rem;
  border-radius: 0.5rem;
}

.mobile-layout .grid-container {
  grid-template-columns: repeat(auto-fill, minmax(16rem, 1fr));
  gap: 1rem;
}

.mobile-layout .load-more-section {
  padding: 1rem 0;
}

.mobile-layout .content-stats {
  font-size: 0.75rem;
  padding: 0.75rem 0;
}
</style>