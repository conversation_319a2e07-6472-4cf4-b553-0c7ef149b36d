<!--
  合集卡片组件
  用于展示合集信息的卡片
-->
<template>
  <div class="collection-card" :class="{ 'mobile-layout': isMobile }" @click="handleCardClick">
    <!-- 卡片封面 -->
    <div class="card-cover">
      <ResponsiveImage v-if="collection.coverImageUrl" :src="collection.coverImageUrl" alt="合集封面" loading="lazy" />
      <div v-else class="cover-placeholder">
        <el-icon :size="32">
          <i-ep-collection />
        </el-icon>
      </div>

      <!-- 状态标签组 -->
      <div class="status-badges">
        <!-- 发布状态标签 -->
        <div class="status-badge publish-status">
          <el-tag :type="collection.status === 'published' ? 'success' : 'warning'" size="small">
            {{ collection.status === 'published' ? '已发布' : '草稿' }}
          </el-tag>
        </div>

        <!-- 可见性标签 -->
        <div class="status-badge visibility-status">
          <el-tag :type="collection.isPublic ? 'success' : 'info'" size="small">
            <el-icon>
              <i-ep-view />
            </el-icon>
            {{ collection.isPublic ? '公开' : '私有' }}
          </el-tag>
        </div>
      </div>





      <!-- 图片底部信息 -->
      <div class="cover-overlay" v-if="showAuthor && collection.creator">
        <div class="overlay-left">
          <div class="overlay-author" @click.stop="goToUserSpace(collection.creator.id)">
            {{ getAuthorDisplayName(collection.creator) }}
          </div>
        </div>
      </div>

      <!-- 收藏按钮（右上角） -->
      <div v-if="showFavoriteButton && collection.isPublic" class="cover-favorite">
        <el-tooltip :content="isFavorited ? '取消收藏' : '收藏'" placement="top" :show-after="300">
          <el-button :type="isFavorited ? 'danger' : 'default'" circle size="small" @click.stop="handleToggleFavorite"
            :class="{ 'is-favorited': isFavorited }" class="favorite-btn" :loading="favoriteLoading">
            <el-icon>
              <i-ep-star-filled v-if="isFavorited" />
              <i-ep-star v-else />
            </el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content">
      <h3 class="card-title">{{ collection.name }}</h3>
      <p v-if="collection.description" class="card-description">{{ collection.description }}</p>

      <!-- 统计信息 -->
      <div class="card-stats">
        <span class="stat-item">
          <el-icon>
            <i-ep-document />
          </el-icon>
          {{ collection.itemCount || 0 }} 个内容
        </span>
        <span v-if="showViewCount" class="stat-item">
          <el-icon>
            <i-ep-view />
          </el-icon>
          {{ collection.viewCount || 0 }} 次浏览
        </span>
        <span v-if="showFavoriteCount" class="stat-item">
          <el-icon>
            <i-ep-star />
          </el-icon>
          {{ collection.favoriteCount || 0 }} 次收藏
        </span>
      </div>



      <!-- 标签 -->
      <div v-if="collection.tags" class="card-tags">
        <el-tag v-for="tag in parseTags(collection.tags)" :key="tag" size="small" class="tag-item">
          {{ tag }}
        </el-tag>
      </div>

      <!-- 操作按钮 -->
      <div class="card-actions" v-if="showActions">
        <el-button-group>
          <el-button size="small" type="primary" @click.stop="handleEdit" title="编辑">
            <el-icon>
              <i-ep-edit />
            </el-icon>
          </el-button>

          <el-button v-if="collection.status === 'draft'" size="small" type="warning" @click.stop="handlePublish"
            title="发布">
            <el-icon>
              <i-ep-upload />
            </el-icon>
          </el-button>
          <el-button v-else size="small" type="info" @click.stop="handleUnpublish" title="下架">
            <el-icon>
              <i-ep-download />
            </el-icon>
          </el-button>
          <el-button size="small" type="danger" @click.stop="handleDelete" title="删除">
            <el-icon>
              <i-ep-delete />
            </el-icon>
          </el-button>
        </el-button-group>
      </div>

      <!-- 更新时间 -->
      <div class="card-footer">
        <span class="card-date">{{ formatDate(collection.updatedAt) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { useCollectionFavoriteStore } from '@/stores/collectionFavoriteStore';
import { isMobileDevice } from '@/utils/deviceDetector';
import { computed } from 'vue';
import ResponsiveImage from '@/components/common/ResponsiveImage.vue';

const router = useRouter();
const collectionFavoriteStore = useCollectionFavoriteStore();

// 设备检测
const isMobile = ref(isMobileDevice());

// Props
const props = defineProps({
  collection: {
    type: Object,
    required: true
  },
  showActions: {
    type: Boolean,
    default: false
  },
  showAuthor: {
    type: Boolean,
    default: false
  },
  showViewCount: {
    type: Boolean,
    default: false
  },
  showFavoriteCount: {
    type: Boolean,
    default: false
  },
  showFavoriteButton: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits([
  'edit',
  'delete',
  'publish',
  'unpublish',
  'toggle-favorite'
]);

// 计算收藏状态
const isFavorited = computed(() => {
  return collectionFavoriteStore.isFavorite(props.collection.id);
});

const favoriteLoading = ref(false);

// 处理卡片点击
const handleCardClick = () => {
  // 检查合集是否可用
  if (props.collection.isAvailable === false) {
    // 显示友好提示
    const reasonText = getUnavailableReasonText(props.collection.unavailableReason);
    ElMessage.warning(`该合集${reasonText}，无法访问`);
    return;
  }

  // 跳转到合集详情页面
  router.push(`/collection/${props.collection.id}`);
};

// 获取不可用原因的文本
const getUnavailableReasonText = (reason) => {
  switch (reason) {
    case 'deleted':
      return '已被删除';
    case 'unpublished':
      return '已下架';
    case 'private':
      return '已设为私有';
    default:
      return '不可用';
  }
};

// 处理编辑
const handleEdit = () => {
  emit('edit', props.collection);
};

// 处理删除
const handleDelete = () => {
  emit('delete', props.collection);
};

// 处理发布
const handlePublish = () => {
  emit('publish', props.collection);
};

// 处理下架
const handleUnpublish = () => {
  emit('unpublish', props.collection);
};



// 处理收藏切换
const handleToggleFavorite = async () => {
  if (favoriteLoading.value) return;

  favoriteLoading.value = true;
  try {
    emit('toggle-favorite', props.collection);
  } finally {
    favoriteLoading.value = false;
  }
};

// 跳转到用户空间
const goToUserSpace = (userId) => {
  router.push(`/user/${userId}`);
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return '';
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  } catch (error) {
    return '';
  }
};

// 解析标签字符串为数组
const parseTags = (tagsStr) => {
  if (!tagsStr) return [];
  if (Array.isArray(tagsStr)) return tagsStr;
  return tagsStr.split(',').filter(tag => tag.trim());
};

// 获取作者显示名称
const getAuthorDisplayName = (creator) => {
  if (!creator) return '未知作者';

  // 优先显示用户名
  if (creator.username && creator.username.trim()) {
    return creator.username;
  }

  // 显示用户ID
  if (creator.id) {
    return creator.id;
  }

  return '未知作者';
};


</script>

<style scoped>
.collection-card {
  border: 1px solid #e4e7ed;
  border-radius: 0.75rem;
  overflow: hidden;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  background: white;
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.08);
}

.collection-card:hover {
  box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
  transform: translateY(-0.125rem);
  border-color: #e6f7ff;
}

.card-cover {
  position: relative;
  width: 100%;
  padding-bottom: 56.25%; /* 16:9 比例 */
  overflow: hidden;
  background: linear-gradient(135deg, #f5f7fa 0%, #e8f4f8 100%);
}



.cover-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
  background: linear-gradient(135deg, #f5f7fa 0%, #e8f4f8 100%);
}

.status-badges {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  z-index: 5;
}

.status-badge {
  display: flex;
}

/* 图片底部覆盖层 */
.cover-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.5);
  padding: 0.5rem 0.75rem;
  display: flex;
  align-items: center;
  z-index: 5;
}

.overlay-left {
  flex: 1;
}

.overlay-author {
  color: #fff;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.overlay-author:hover {
  text-decoration: underline;
}



.cover-favorite {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  z-index: 10;
}

.cover-favorite .favorite-btn {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1);
  min-width: 2.25rem;
  min-height: 2.25rem;
}

.cover-favorite .favorite-btn:hover {
  background-color: rgba(255, 255, 255, 1);
  transform: scale(1.1);
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.15);
}

.cover-favorite .favorite-btn.is-favorited {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: #ffffff;
  border-color: #ff6b6b;
}

.cover-favorite .favorite-btn.is-favorited:hover {
  background: linear-gradient(135deg, #ff5252 0%, #e53935 100%);
}

/* 统一收藏按钮图标大小 */
.cover-favorite .favorite-btn .el-icon {
  font-size: 1.125rem !important;
  width: 1.125rem;
  height: 1.125rem;
}

/* 统一统计信息中的图标大小 */
.card-stats .stat-item .el-icon {
  font-size: 1rem !important;
  width: 1rem;
  height: 1rem;
}



.card-content {
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #303133;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-description {
  font-size: 0.875rem;
  color: #606266;
  margin: 0 0 0.75rem 0;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-stats {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}



.card-tags {
  margin-bottom: 8px;
}

.tag-item {
  margin-right: 6px;
  margin-bottom: 4px;
}

.card-actions {
  margin-bottom: 8px;
}

.card-favorite {
  margin-bottom: 0.75rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.card-favorite .el-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1);
}

/* 已收藏状态样式 */
.card-favorite .el-button.is-favorited {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: #ffffff;
  border-color: #ff6b6b;
  transform: scale(1.05);
}

.card-favorite .el-button.is-favorited:hover {
  background: linear-gradient(135deg, #ff5252 0%, #e53935 100%);
  transform: scale(1.1);
}

/* 未收藏状态悬停样式 */
.card-favorite .el-button:not(.is-favorited):hover {
  color: #ff6b6b;
  border-color: #ff6b6b;
  background-color: rgba(255, 107, 107, 0.1);
  transform: scale(1.05);
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 4px;
  /* 移除横线 */
}

.card-date {
  font-size: 12px;
  color: #c0c4cc;
}

.favorite-btn {
  transition: all 0.3s ease;
  transform: scale(1);
}

.favorite-btn:hover {
  transform: scale(1.1);
}

.favorite-btn.is-favorited {
  animation: favorite-pop 0.3s ease;
}

@keyframes favorite-pop {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}

/* 手机端样式优化 */
.mobile-layout.collection-card {
  box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.06);
}

.mobile-layout .card-cover {
  padding-bottom: 56.25%; /* 保持16:9比例 */
}

.mobile-layout .card-content {
  padding: 0.625rem;
}

.mobile-layout .card-title {
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
  -webkit-line-clamp: 1;
  /* 只显示一行标题 */
}

.mobile-layout .card-description {
  font-size: 0.75rem;
  -webkit-line-clamp: 2;
  /* 最多显示两行描述 */
  margin-bottom: 0.5rem;
}

.mobile-layout .card-author {
  font-size: 0.625rem;
  margin-bottom: 0.25rem;
}

.mobile-layout .card-tags {
  margin-bottom: 0.25rem;
}

.mobile-layout .tag-item {
  font-size: 0.625rem;
  padding: 0 0.25rem;
  margin-right: 0.25rem;
  margin-bottom: 0.25rem;
}

.mobile-layout .card-actions {
  margin-bottom: 0.25rem;
}

.mobile-layout .card-actions .el-button {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

.mobile-layout .card-footer {
  padding-top: 0.25rem;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.125rem;
}

.mobile-layout .card-date {
  font-size: 0.625rem;
  width: 100%;
  text-align: right;
}



.mobile-layout .status-badge .el-tag {
  font-size: 0.625rem;
  padding: 0 0.25rem;
}

/* 统一的响应式设计，不使用媒体查询 */
</style>
