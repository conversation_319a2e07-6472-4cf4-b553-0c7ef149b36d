<!--
  等级选择引导组件
  首次访问时引导用户选择日语等级
-->
<template>
  <el-dialog
    v-model="visible"
    title="选择你的日语水平"
    :width="isMobile ? '90%' : '32rem'"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
  >
    <div class="level-guide">
      <p class="guide-text">为了给你推荐合适的学习内容，请选择你的日语水平：</p>
      
      <div class="level-options">
        <div
          v-for="level in USER_LEVELS"
          :key="level.key"
          class="level-option"
          :class="{ 'selected': selectedLevel === level.key }"
          @click="selectedLevel = level.key"
        >
          <div class="level-name">{{ level.name }}</div>
          <div class="level-desc">{{ level.description }}</div>
        </div>
      </div>
      
      <div class="guide-actions">
        <el-button @click="handleSkip" size="large">跳过</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm" 
          :disabled="!selectedLevel"
          size="large"
        >
          确定
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue';
import { isMobileDevice } from '@/utils/deviceDetector';
import { USER_LEVELS, setUserLevel } from '@/utils/userPreferences';

const props = defineProps({
  modelValue: Boolean
});

const emit = defineEmits(['update:modelValue', 'level-selected']);

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const isMobile = computed(() => isMobileDevice());
const selectedLevel = ref('');

const handleConfirm = () => {
  if (selectedLevel.value) {
    setUserLevel(selectedLevel.value);
    emit('level-selected', selectedLevel.value);
    visible.value = false;
  }
};

const handleSkip = () => {
  visible.value = false;
};
</script>

<style scoped>
.level-guide {
  padding: 1rem 0;
}

.guide-text {
  text-align: center;
  color: #606266;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

.level-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 2rem;
}

.level-option {
  padding: 1rem;
  border: 0.125rem solid #e4e7ed;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s;
}

.level-option:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.level-option.selected {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.level-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 0.25rem;
}

.level-desc {
  font-size: 0.875rem;
  color: #909399;
}

.guide-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

/* 移动端优化 */
.mobile-layout .level-option {
  padding: 0.875rem;
}

.mobile-layout .level-name {
  font-size: 0.9rem;
}

.mobile-layout .level-desc {
  font-size: 0.8rem;
}
</style>