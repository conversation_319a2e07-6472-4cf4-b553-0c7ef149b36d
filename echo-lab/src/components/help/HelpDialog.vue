<!--
  通用帮助对话框组件
  支持不同页面的帮助内容展示
  桌面端使用对话框，移动端使用抽屉
-->
<template>
  <!-- 桌面端对话框 -->
  <StandardDialog v-if="!isMobile" v-model="dialogVisible" :title="helpConfig.title" width="80%" height="70vh"
    :show-confirm="false" :cancel-text="'关闭'" class="help-dialog">
    <HelpContent :help-config="helpConfig" :active-section="activeSection" :current-section="currentSection"
      :expanded-items="expandedItems" @section-change="handleSectionChange" @toggle-item="toggleItem" />
  </StandardDialog>

  <!-- 移动端抽屉 -->
  <el-drawer v-else v-model="dialogVisible" :title="helpConfig.title" direction="btt" size="85%" class="help-drawer">
    <HelpContent :help-config="helpConfig" :active-section="activeSection" :current-section="currentSection"
      :expanded-items="expandedItems" @section-change="handleSectionChange" @toggle-item="toggleItem" />
  </el-drawer>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import StandardDialog from '../common/StandardDialog.vue';
import HelpContent from './HelpContent.vue';
import { isMobileDevice } from '@/utils/deviceDetector';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  helpConfig: {
    type: Object,
    required: true,
    default: () => ({
      title: '帮助',
      sections: []
    })
  }
});

const emit = defineEmits(['update:modelValue']);

// 移动设备检测
const isMobile = ref(isMobileDevice());

// 对话框可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});

// 当前激活的章节
const activeSection = ref('');
const expandedItems = ref(new Set());

// 当前章节内容
const currentSection = computed(() => {
  if (!props.helpConfig.sections || props.helpConfig.sections.length === 0) {
    return null;
  }

  return props.helpConfig.sections.find(section => section.id === activeSection.value) ||
    props.helpConfig.sections[0];
});

// 切换章节
const handleSectionChange = (sectionId) => {
  activeSection.value = sectionId;
  // 切换章节时清空展开状态
  expandedItems.value.clear();
};

// 切换帮助项目展开状态
const toggleItem = (itemId) => {
  if (expandedItems.value.has(itemId)) {
    expandedItems.value.delete(itemId);
  } else {
    expandedItems.value.add(itemId);
  }
};

// 监听帮助配置变化，初始化激活章节
watch(() => props.helpConfig, (newConfig) => {
  if (newConfig.sections && newConfig.sections.length > 0) {
    activeSection.value = newConfig.sections[0].id;
  }
  expandedItems.value.clear();
}, { immediate: true });

// 监听对话框打开状态，重置展开状态
watch(dialogVisible, (visible) => {
  if (visible) {
    expandedItems.value.clear();
    if (props.helpConfig.sections && props.helpConfig.sections.length > 0) {
      activeSection.value = props.helpConfig.sections[0].id;
    }
  }
});
</script>

<style scoped>
.help-dialog {
  --el-dialog-padding-primary: 0;
}

:deep(.el-dialog__body) {
  padding: 0;
  height: 70vh;
  overflow: hidden;
}

.help-drawer :deep(.el-drawer__body) {
  padding: 0;
}
</style>
