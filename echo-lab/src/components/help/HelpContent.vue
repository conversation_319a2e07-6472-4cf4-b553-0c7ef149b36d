<!--
  帮助内容组件
  可在对话框和抽屉中复用
-->
<template>
  <div class="help-content">
    <!-- 帮助导航 -->
    <div class="help-nav" v-if="helpConfig.sections && helpConfig.sections.length > 1">
      <el-menu :default-active="activeSection" @select="handleSectionChange" mode="horizontal" class="help-nav-menu">
        <el-menu-item v-for="section in helpConfig.sections" :key="section.id" :index="section.id">
          <el-icon v-if="section.icon">
            <component :is="section.icon" />
          </el-icon>
          {{ section.title }}
        </el-menu-item>
      </el-menu>
    </div>

    <!-- 帮助内容区域 -->
    <div class="help-body">
      <div class="help-section" v-if="currentSection">
        <h2 class="section-title">{{ currentSection.title }}</h2>
        <div class="section-description" v-if="currentSection.description">
          {{ currentSection.description }}
        </div>

        <!-- 渲染帮助项目 -->
        <div class="help-items">
          <div v-for="item in currentSection.items" :key="item.id" class="help-item"
            :class="{ 'expandable': item.details }">
            <div class="item-header" @click="handleToggleItem(item.id)">
              <div class="item-title">
                <el-icon v-if="item.icon" class="item-icon">
                  <component :is="item.icon" />
                </el-icon>
                <span>{{ item.title }}</span>
              </div>
              <el-icon v-if="item.details" class="expand-icon" :class="{ 'expanded': expandedItems.has(item.id) }">
                <i-ep-arrow-down />
              </el-icon>
            </div>

            <div class="item-content">
              <p class="item-description">{{ item.description }}</p>

              <!-- 详细内容（可展开） -->
              <el-collapse-transition>
                <div v-if="item.details && expandedItems.has(item.id)" class="item-details">
                  <div v-for="detail in item.details" :key="detail.id" class="detail-item">
                    <h4 v-if="detail.title" class="detail-title">{{ detail.title }}</h4>
                    <p v-if="detail.content" class="detail-content">{{ detail.content }}</p>

                    <!-- 步骤列表 -->
                    <ol v-if="detail.steps" class="detail-steps">
                      <li v-for="step in detail.steps" :key="step">{{ step }}</li>
                    </ol>

                    <!-- 提示列表 -->
                    <ul v-if="detail.tips" class="detail-tips">
                      <li v-for="tip in detail.tips" :key="tip">{{ tip }}</li>
                    </ul>
                  </div>
                </div>
              </el-collapse-transition>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 图标现在通过 unplugin-icons 自动导入，无需手动导入

const props = defineProps({
  helpConfig: {
    type: Object,
    required: true
  },
  activeSection: {
    type: String,
    required: true
  },
  currentSection: {
    type: Object,
    default: null
  },
  expandedItems: {
    type: Set,
    required: true
  }
});

const emit = defineEmits(['section-change', 'toggle-item']);

const handleSectionChange = (sectionId) => {
  emit('section-change', sectionId);
};

const handleToggleItem = (itemId) => {
  emit('toggle-item', itemId);
};
</script>

<style scoped>
.help-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.help-nav {
  border-bottom: 1px solid #e4e7ed;
  background-color: #fafafa;
}

.help-nav-menu {
  border-bottom: none;
}

:deep(.help-nav-menu .el-menu-item) {
  height: 2.5rem;
  line-height: 2.5rem;
  font-size: 0.875rem;
  padding: 0 1rem;
}

.help-body {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.section-title {
  font-size: 1.25rem;
  color: #303133;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.section-description {
  color: #606266;
  margin-bottom: 1rem;
  line-height: 1.5;
  font-size: 0.9rem;
}

.help-items {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.help-item {
  border: 1px solid #e4e7ed;
  border-radius: 0.5rem;
  overflow: hidden;
  transition: all 0.3s ease;
}

.help-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background-color: #f8f9fa;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.expandable .item-header:hover {
  background-color: #e9ecef;
}

.item-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #303133;
  font-size: 0.95rem;
}

.item-icon {
  color: #409eff;
}

.expand-icon {
  transition: transform 0.3s ease;
  color: #909399;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.item-content {
  padding: 0.75rem;
}

.item-description {
  color: #606266;
  line-height: 1.5;
  margin: 0;
  font-size: 0.9rem;
}

.item-details {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid #e4e7ed;
}

.detail-item {
  margin-bottom: 0.75rem;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-title {
  font-size: 0.95rem;
  color: #303133;
  margin-bottom: 0.375rem;
  font-weight: 500;
}

.detail-content {
  color: #606266;
  line-height: 1.5;
  margin-bottom: 0.375rem;
  font-size: 0.875rem;
}

.detail-steps {
  margin: 0.375rem 0;
  padding-left: 1.25rem;
}

.detail-steps li {
  color: #606266;
  line-height: 1.5;
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.detail-tips {
  margin: 0.375rem 0;
  padding-left: 1.25rem;
}

.detail-tips li {
  color: #606266;
  line-height: 1.5;
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}
</style>
