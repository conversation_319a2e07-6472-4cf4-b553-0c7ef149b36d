<!--
  帮助按钮组件
  可以集成到任何页面中，提供统一的帮助入口
-->
<template>
  <el-button :type="type" :size="size" :circle="circle" :link="link" @click="handleClick"
    :class="['help-button', customClass]" :title="tooltip">
    <el-icon>
      <i-ep-question-filled />
    </el-icon>
    <span v-if="!circle && showText">{{ buttonText }}</span>
  </el-button>
</template>

<script setup>
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import { useHelp } from '@/composables/useHelp';

const props = defineProps({
  // 页面类型，用于确定显示哪个帮助内容
  pageType: {
    type: String,
    required: true
  },
  // 按钮样式
  type: {
    type: String,
    default: 'default'
  },
  size: {
    type: String,
    default: 'default'
  },
  // 是否为圆形按钮
  circle: {
    type: Boolean,
    default: false
  },
  // 是否为链接按钮
  link: {
    type: Boolean,
    default: false
  },
  // 是否显示文字
  showText: {
    type: Boolean,
    default: true
  },
  // 按钮文字
  buttonText: {
    type: String,
    default: '帮助'
  },
  // 提示文字
  tooltip: {
    type: String,
    default: '查看使用帮助'
  },
  // 自定义样式类
  customClass: {
    type: String,
    default: ''
  }
});

const { showHelp } = useHelp();

const handleClick = () => {
  showHelp(props.pageType);
};
</script>

<style scoped>
.help-button {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.help-button.is-circle {
  width: 2rem;
  height: 2rem;
}

/* 不同尺寸的圆形按钮 */
.help-button.is-circle.el-button--small {
  width: 1.5rem;
  height: 1.5rem;
}

.help-button.is-circle.el-button--large {
  width: 2.5rem;
  height: 2.5rem;
}
</style>
