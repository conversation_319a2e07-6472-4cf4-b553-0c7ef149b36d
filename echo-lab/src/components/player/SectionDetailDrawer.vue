<script setup>
import { ref, computed, watch } from 'vue';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import { ElMessage, ElMessageBox } from 'element-plus';
import { getLanguageLabel } from '@/config/languages';
import { isMobileDevice } from '@/utils/deviceDetector';
import NumberEditor from '../common/NumberEditor.vue';

// 接收属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  section: {
    type: Object,
    default: () => ({})
  },
  content: {
    type: Object,
    required: false,
    default: () => ({})
  }
});

// 事件
const emit = defineEmits(['update:visible', 'update:section', 'save', 'apply-template']);

// 本地编辑状态
const localSection = ref({});



// 检测是否为移动设备
const isMobile = isMobileDevice();

// 初始化每次重复的参数
const initCustomRepeatSettings = () => {
  const repeatCount = localSection.value.repeatCount || 4;

  // 使用固定的默认值
  const defaultSpeed = 1.0;
  const defaultPause = localSection.value.pauseDuration || 3000;

  // 初始化重复速度数组
  if (!localSection.value.repeatSpeeds || !Array.isArray(localSection.value.repeatSpeeds)) {
    localSection.value.repeatSpeeds = [];
  }

  // 初始化重复停顿时长数组
  if (!localSection.value.repeatPauses || !Array.isArray(localSection.value.repeatPauses)) {
    localSection.value.repeatPauses = [];
  }

  // 调整数组长度为重复次数
  while (localSection.value.repeatSpeeds.length < repeatCount) {
    localSection.value.repeatSpeeds.push(defaultSpeed);
  }

  while (localSection.value.repeatPauses.length < repeatCount) {
    localSection.value.repeatPauses.push(defaultPause);
  }

  // 如果数组过长，截断
  if (localSection.value.repeatSpeeds.length > repeatCount) {
    localSection.value.repeatSpeeds = localSection.value.repeatSpeeds.slice(0, repeatCount);
  }

  if (localSection.value.repeatPauses.length > repeatCount) {
    localSection.value.repeatPauses = localSection.value.repeatPauses.slice(0, repeatCount);
  }
};

// 监听section变化，更新本地编辑状态
watch(() => props.section, (newSection) => {
  if (newSection) {
    localSection.value = JSON.parse(JSON.stringify(newSection));
    // 始终初始化重复参数
    initCustomRepeatSettings();
  }
}, { immediate: true, deep: true });

// 监听visible变化，重置本地编辑状态
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.section) {
    localSection.value = JSON.parse(JSON.stringify(props.section));
    // 始终初始化重复参数
    initCustomRepeatSettings();
  }
}, { immediate: true });

// 计算属性：可用的翻译语言
const availableTranslationLanguages = computed(() => {
  if (!props.content || !props.content.configJson || !props.content.configJson.resources || !props.content.configJson.resources.translations) {
    return [];
  }

  // 从资源中获取可用的翻译语言
  const translations = props.content.configJson.resources.translations;
  const languages = Object.keys(translations);

  // 使用getLanguageLabel函数获取语言显示名称
  return languages.map(code => ({
    value: code,
    label: getLanguageLabel(code)
  }));
});



// 关闭抽屉
const closeDrawer = () => {
  emit('update:visible', false);
};

// 取消更改
const cancelChanges = () => {
  closeDrawer();
};

// 处理重复次数变化
const handleRepeatChange = (value) => {
  // 确保翻译插入位置不超过重复次数
  if (localSection.value.enableTranslation) {
    if (localSection.value.translationPosition > value) {
      localSection.value.translationPosition = value;
    }

    // 如果重复次数为1，强制将翻译插入位置设置为1
    if (value === 1) {
      localSection.value.translationPosition = 1;
    }
  }

  // 确保关键词插入位置不超过重复次数
  if (localSection.value.enableKeywords) {
    if (localSection.value.keywordPosition > value) {
      localSection.value.keywordPosition = value;
    }

    // 如果重复次数为1，强制将关键词插入位置设置为1
    if (value === 1) {
      localSection.value.keywordPosition = 1;
    }
  }

  // 始终更新重复速度和停顿时长数组
  initCustomRepeatSettings();
};

// 保存更改
const saveChanges = () => {
  // 验证重复次数
  if (localSection.value.repeatCount < 1 || localSection.value.repeatCount > 10) {
    ElMessage.warning('重复次数必须在1-10之间');
    return;
  }

  // 移除语速验证，因为已经移除了语速字段



  // 确保重复参数数组长度正确
  initCustomRepeatSettings();

  // 更新section
  emit('update:section', localSection.value);

  // 只发送update事件，不触发save事件，避免立即重新生成视频
  // 显示保存成功消息
  ElMessage.success('环节设置已保存');

  // 关闭抽屉
  closeDrawer();
};



// 处理翻译启用状态变化
const handleTranslationEnableChange = (enabled) => {
  localSection.value.enableTranslation = enabled;
  if (enabled && !localSection.value.translationLanguage) {
    // 默认选择中文
    localSection.value.translationLanguage = 'zh-CN';
  }
};

// 处理关键词启用状态变化
const handleKeywordEnableChange = (enabled) => {
  localSection.value.enableKeywords = enabled;
};


</script>

<template>
  <div>
    <el-drawer :model-value="visible" @update:model-value="$emit('update:visible', $event)"
      :title="localSection.name || '环节详情'" :direction="isMobile ? 'btt' : 'rtl'" :size="isMobile ? '70%' : '40%'"
      :with-header="true" :destroy-on-close="false" :close-on-click-modal="false" :close-on-press-escape="true"
      class="section-detail-drawer" :show-close="false">
      <template #header>
        <div class="drawer-header" :class="{ 'mobile-header': isMobile, 'desktop-header': !isMobile }">
          <div class="header-left">
            <el-button @click="closeDrawer" text class="back-button">
              <el-icon>
                <i-ep-arrow-left />
              </el-icon>
              返回列表
            </el-button>
          </div>
          <div class="header-center">
            <span class="drawer-title">{{ localSection.name || '环节详情' }}</span>
          </div>
          <div class="header-right">
            <!-- 桌面端可以添加额外的操作按钮 -->
          </div>
        </div>
      </template>

      <div class="drawer-content">
        <el-form label-position="top" :model="localSection" class="section-form">



          <!-- 播放设置 -->
          <div class="settings-section">
            <div class="settings-section-title">播放设置</div>
            <div class="form-section-row">
              <div class="setting-item">
                <div class="setting-label">停顿时长</div>
                <NumberEditor v-model="localSection.pauseDuration" :min="0" :max="5000" :step="100" suffix="ms"
                  class="setting-control" />
              </div>
            </div>
          </div>

          <!-- 重复设置 -->
          <div class="settings-section">
            <div class="settings-section-title">重复设置</div>
            <div class="form-section-row">
              <div class="setting-item">
                <div class="setting-label">重复次数</div>
                <NumberEditor v-model="localSection.repeatCount" :min="1" :max="10" :step="1" class="setting-control"
                  @change="handleRepeatChange" />
              </div>
            </div>

            <div class="section-subtitle" style="margin-top: 1rem;">
              <div class="title-with-tooltip">
                <span class="setting-label">每次重复的参数</span>
                <el-tooltip content="可以为每次重复设置不同的速度和停顿时长" placement="top">
                  <el-icon class="question-icon">
                    <i-ep-question-filled />
                  </el-icon>
                </el-tooltip>
              </div>
            </div>

            <div class="custom-repeat-settings">
              <div class="custom-repeat-header">
                <div class="repeat-index-header">重复次数</div>
                <div class="repeat-speed-header">播放速度</div>
                <div class="repeat-pause-header">停顿时长(ms)</div>
              </div>
              <div class="custom-repeat-body">
                <div v-for="(_, index) in Array(localSection.repeatCount)" :key="index" class="custom-repeat-row">
                  <div class="repeat-index">第 {{ index + 1 }} 次</div>
                  <div class="repeat-speed">
                    <NumberEditor v-model="localSection.repeatSpeeds[index]" :min="0.9" :max="1.1" :step="0.1"
                      :precision="1" suffix="x" class="full-width" />
                  </div>
                  <div class="repeat-pause">
                    <NumberEditor v-model="localSection.repeatPauses[index]" :min="0" :max="5000" :step="100"
                      class="full-width" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 翻译设置 -->
          <div class="settings-section">
            <div class="section-title-with-switch">
              <div class="title-with-tooltip">
                <span class="settings-section-title">翻译设置</span>
                <el-tooltip content="在重复播放中插入翻译音频" placement="top">
                  <el-icon class="question-icon">
                    <i-ep-question-filled />
                  </el-icon>
                </el-tooltip>
              </div>
              <el-switch v-model="localSection.enableTranslation" @change="handleTranslationEnableChange" />
            </div>

            <template v-if="localSection.enableTranslation">
              <div class="form-section-row">
                <div class="setting-item">
                  <div class="setting-label">翻译语言</div>
                  <el-select v-model="localSection.translationLanguage" placeholder="请选择翻译语言" class="setting-control">
                    <el-option v-for="lang in availableTranslationLanguages" :key="lang.value" :label="lang.label"
                      :value="lang.value" />
                    <el-empty v-if="availableTranslationLanguages.length === 0" description="没有可用的翻译语言"
                      :image-size="60">
                    </el-empty>
                  </el-select>
                  <div class="form-help" v-if="availableTranslationLanguages.length === 0">
                    当前内容没有可用的翻译语言
                  </div>
                </div>
              </div>

              <div class="form-section-row">
                <div class="setting-item">
                  <div class="setting-label">插入位置</div>
                  <NumberEditor v-model="localSection.translationPosition" :min="1" :max="localSection.repeatCount || 4"
                    :step="1" class="setting-control" :disabled="localSection.repeatCount === 1" />
                  <div class="form-help" v-if="localSection.repeatCount > 1">
                    在第几次重复后插入翻译（1-{{ localSection.repeatCount || 4 }}）
                  </div>
                  <div class="form-help" v-else>
                    重复次数为1时，翻译将在内容播放后立即插入
                  </div>
                </div>
              </div>
            </template>
          </div>

          <!-- 关键词设置 -->
          <div class="settings-section">
            <div class="section-title-with-switch">
              <div class="title-with-tooltip">
                <span class="settings-section-title">关键词设置</span>
                <el-tooltip content="在重复播放中插入关键词音频" placement="top">
                  <el-icon class="question-icon">
                    <i-ep-question-filled />
                  </el-icon>
                </el-tooltip>
              </div>
              <el-switch v-model="localSection.enableKeywords" @change="handleKeywordEnableChange" />
            </div>

            <template v-if="localSection.enableKeywords">
              <div class="form-section-row">
                <div class="setting-item">
                  <div class="setting-label">插入位置</div>
                  <NumberEditor v-model="localSection.keywordPosition" :min="1" :max="localSection.repeatCount || 4"
                    :step="1" class="setting-control" :disabled="localSection.repeatCount === 1" />
                  <div class="form-help" v-if="localSection.repeatCount > 1">
                    在第几次重复后插入关键词（1-{{ localSection.repeatCount || 4 }}）
                  </div>
                  <div class="form-help" v-else>
                    重复次数为1时，关键词将在内容播放后立即插入
                  </div>
                </div>
              </div>

              <div class="form-section-row">
                <div class="setting-item">
                  <div class="setting-label">重复次数</div>
                  <NumberEditor v-model="localSection.keywordRepeatCount" :min="1" :max="5" :step="1"
                    class="setting-control" />
                </div>
              </div>


            </template>
          </div>
        </el-form>
      </div>

      <template #footer>
        <div class="drawer-footer">
          <div class="footer-buttons">
            <el-button @click="cancelChanges" class="cancel-button">取消</el-button>
            <el-button type="primary" @click="saveChanges" class="save-button">保存</el-button>
          </div>
        </div>
      </template>
    </el-drawer>


  </div>
</template>

<style scoped>
.section-detail-drawer {
  --el-drawer-padding-primary: 0;
}

:deep(.el-drawer__body) {
  display: flex;
  flex-direction: column;
  padding: 0;
  overflow: hidden;
}

/* 桌面端特有样式 */
:deep(.el-drawer[rtl="true"]) {
  max-width: 500px;
  /* 限制最大宽度 */
}

/* 移动端特有样式 */
:deep(.el-drawer[rtl="false"]) {
  border-radius: 0.75rem 0.75rem 0 0;
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  border-bottom: 0.0625rem solid #ebeef5;
}

/* 移动端头部样式 */
.mobile-header .header-left,
.mobile-header .header-right {
  flex: 1;
}

.mobile-header .header-center {
  flex: 2;
  text-align: center;
}

/* 桌面端头部样式 */
.desktop-header .header-left {
  flex: 0 0 auto;
}

.desktop-header .header-center {
  flex: 1;
  text-align: left;
  margin-left: 1rem;
}

.desktop-header .header-right {
  flex: 0 0 auto;
}

.drawer-title {
  font-size: 1.125rem;
  font-weight: 500;
}

.back-button {
  padding: 0.25rem 0.5rem;
}

.drawer-content {
  overflow-y: auto;
  flex: 1;
  background-color: #f5f7fa;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  padding: 1rem;
  border-top: 0.0625rem solid #ebeef5;
  background-color: #fff;
}

.footer-buttons {
  display: flex;
  gap: 1.5rem;
}

.cancel-button,
.save-button {
  min-width: 6rem;
  padding: 0.75rem 1.5rem;
}

.section-form {
  width: 100%;
}

.processing-mode-section {
  margin-top: 1rem;
}

.processing-mode-group {
  margin-top: 0.5rem;
}

.form-section-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-section-row>* {
  flex: 1;
  min-width: 45%;
}

.source-nodes-select {
  width: 100%;
}

.settings-section {
  margin-bottom: 1.5rem;
  padding: 1rem;
  border-radius: 0.5rem;
  background-color: #fff;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.05);
}

.settings-section:last-child {
  margin-bottom: 0;
}

.settings-section-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: #303133;
  margin-bottom: 0;
  padding-bottom: 0;
}

.section-subtitle {
  margin-bottom: 0.75rem;
  font-size: 1rem;
  font-weight: 500;
  color: #303133;
}

.section-title-with-switch {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 0.0625rem solid #f0f0f0;
  font-size: 1rem;
  font-weight: 500;
  color: #303133;
}

.title-with-tooltip {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.125rem;
  font-weight: 500;
}

.question-icon {
  font-size: 1rem;
  color: #909399;
  cursor: help;
  display: flex;
  align-items: center;
}

.setting-item {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 45%;
}

/* 自定义重复设置样式 */
.custom-repeat-settings {
  margin: 0.5rem 0 1rem;
  border: 0.0625rem solid #ebeef5;
  border-radius: 0.25rem;
  overflow: hidden;
  box-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.05);
}

.custom-repeat-header {
  display: flex;
  background-color: #f5f7fa;
  padding: 0.5rem;
  font-weight: bold;
  border-bottom: 0.0625rem solid #ebeef5;
}

.custom-repeat-body {
  max-height: 15rem;
  overflow-y: auto;
}

.custom-repeat-row {
  display: flex;
  padding: 0.5rem;
  border-bottom: 0.0625rem solid #ebeef5;
  align-items: center;
}

.custom-repeat-row:last-child {
  border-bottom: none;
}

.repeat-index-header,
.repeat-index {
  padding: 0 0.25rem;
}

.repeat-speed-header,
.repeat-speed {
  flex: 1;
  padding: 0 0.25rem;
}

.repeat-pause-header,
.repeat-pause {
  flex: 1.2;
  padding: 0 0.25rem;
}

.setting-label {
  font-size: 1rem;
  margin-bottom: 0.5rem;
  color: #606266;
}

.setting-control {
  width: 100%;
}

.form-help {
  font-size: 0.75rem;
  color: #909399;
  margin-top: 0.25rem;
  line-height: 1.2;
}



:deep(.el-form-item) {
  margin-bottom: 0.5rem;
}

:deep(.el-form-item__label) {
  padding-bottom: 0.25rem;
  line-height: 1.5;
  font-size: 1rem;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-slider) {
  margin-top: 0.5rem;
}

.full-width {
  width: 100%;
}

/* 模板选择器样式 */
.template-selector-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 0.5rem;
  border: 1px solid #e4e7ed;
}

.current-template-info {
  flex: 1;
}

.template-name {
  font-weight: 500;
  color: #409eff;
}

.no-template {
  color: #909399;
  font-style: italic;
}

.template-actions {
  display: flex;
  gap: 0.5rem;
}
</style>
