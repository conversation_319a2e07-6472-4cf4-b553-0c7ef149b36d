<script setup>
import { computed } from 'vue';
import { generateRubyHTML } from '../../utils/rubyGenerator';
import ResponsiveImage from '@/components/common/ResponsiveImage.vue';

// 接收属性
const props = defineProps({
  // 内容项
  item: {
    type: Object,
    required: true
  },
  // 版权信息（独立于item）
  copyright: {
    type: Object,
    default: () => null
  },
  // 文本样式
  textStyle: {
    type: Object,
    default: () => ({
      fontSize: 2.0,
      color: '#FFFFFF',
      textShadow: '0.0625rem 0.0625rem 0.125rem rgba(0, 0, 0, 0.7)',
      backgroundColor: '#000000'
    })
  }
});

// 事件
const emit = defineEmits(['content-element-click']);

// 自动判断内容类型
const getContentType = (item) => {
  if (!item) return 'text';
  if (item.imageUrl) return 'image';
  if (item.content) return 'text';
  return 'text';
};

// 内容类型
const contentType = computed(() => {
  return getContentType(props.item);
});



// 带有Ruby标签的内容
const rubyContent = computed(() => {
  // 如果item不存在或不是文本内容，返回空
  if (!props.item || contentType.value !== 'text') {
    return '';
  }

  // 如果没有内容或标注，直接返回原始内容
  if (!props.item.content || !props.item.annotation) {
    return props.item.content || '';
  }

  // 所有语言都可以显示标注，不再限制只有日语

  // 生成带有Ruby标签的HTML内容
  return generateRubyHTML(props.item.content, props.item.annotation);
});



// 是否显示版权信息
const showCopyright = computed(() => {
  return props.copyright && props.copyright.text;
});

// 版权文本
const copyrightText = computed(() => {
  return props.copyright?.text || '';
});

// 版权位置
const copyrightPosition = computed(() => {
  return props.copyright?.position || 'bottomRight';
});



// 获取内容属性
const getContentProps = () => {
  if (contentType.value === 'text') {
    return {
      class: 'content-text',
      style: {
        fontSize: typeof props.textStyle.fontSize === 'number' ? `${props.textStyle.fontSize}rem` : props.textStyle.fontSize,
        color: props.textStyle.color,
        textShadow: props.textStyle.textShadow
      },
      innerHTML: rubyContent.value,
      onClick: handleContentElementClick
    };
  }
  
  return {};
};

// 处理内容元素点击
const handleContentElementClick = (event) => {
  // 阻止事件冒泡，避免触发播放/暂停
  event.stopPropagation();

  // 获取点击的元素
  const target = event.target;

  // 如果点击的是ruby标签或其子元素，提取相关信息
  if (target.tagName === 'RUBY' || target.closest('ruby')) {
    const rubyElement = target.tagName === 'RUBY' ? target : target.closest('ruby');
    const originalText = rubyElement.textContent.replace(/\s+/g, ''); // 去除空白字符

    // 发送点击事件，包含原始文本和位置信息
    emit('content-element-click', {
      text: originalText,
      element: rubyElement,
      position: {
        x: event.clientX,
        y: event.clientY
      }
    });
  }
};
</script>

<template>
  <div class="content-display" :style="{ backgroundColor: textStyle.backgroundColor }">
    <!-- 主要内容 -->
    <div class="main-content">
      <!-- 图片内容 -->
      <div v-if="contentType === 'image'" class="image-container">
        <ResponsiveImage v-if="item.imageUrl" :src="item.imageUrl" 
             alt="Content Image" 
             loading="lazy"
             class="content-image" />
      </div>
      
      <!-- 文本内容 -->
      <div v-else v-bind="getContentProps()" />
    </div>

    <!-- 版权信息 -->
    <div v-if="showCopyright" :class="['copyright-text', `position-${copyrightPosition}`]" :style="{
      fontSize: typeof textStyle.fontSize === 'number' ? `${textStyle.fontSize * 0.7}rem` :
        (typeof textStyle.fontSize === 'string' && textStyle.fontSize.endsWith('px')) ?
          `${parseFloat(textStyle.fontSize) * 0.7}px` : textStyle.fontSize,
      color: textStyle.color
    }">
      {{ copyrightText }}
    </div>
  </div>
</template>

<style scoped>
.content-display {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.main-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.image-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.content-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.content-text {
  text-align: center;
  max-width: 90%;
  line-height: 2;
  padding: 0.75rem 1.25rem;
  display: inline-block;
  transition: color 0.3s ease;
  box-sizing: border-box;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.copyright-text {
  position: absolute;
  text-align: center;
  opacity: 0.8;
  font-style: italic;
  padding: 0.5rem;
}

.position-bottomRight {
  bottom: 1rem;
  right: 1rem;
}

.position-bottomLeft {
  bottom: 1rem;
  left: 1rem;
}

.position-topRight {
  top: 1rem;
  right: 1rem;
}

.position-topLeft {
  top: 1rem;
  left: 1rem;
}

/* Ruby标签样式 */
.content-text :deep(ruby) {
  margin: 0 0.1em;
  cursor: pointer;
  transition: background-color 0.2s;
}

.content-text :deep(ruby:hover) {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 0.25rem;
}

.content-text :deep(rt) {
  font-size: 0.6em;
  color: inherit;
  opacity: 0.9;
}
</style>
