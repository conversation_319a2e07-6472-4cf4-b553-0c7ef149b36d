/**
 * SEO管理组合式函数
 * 用于动态设置页面标题、meta标签和结构化数据
 */
import { ref, onMounted, onUnmounted, watch } from "vue";
import { useRoute } from "vue-router";

// 默认SEO配置
const DEFAULT_SEO = {
  title: "Echo Lab - 日语精听工具",
  description:
    "Echo Lab 是一个高效的精听学习工具，帮助你通过重复听力快速提升语言理解力。",
  keywords: "日语,精听,听力训练,语言学习,日本语,N2,N3,N4,N5",
  image: "/icons/logo-512.png",
  url: "https://echolab.club",
  type: "website",
};

// 存储原始标题
let originalTitle = "";
let originalMeta = new Map();

/**
 * 设置页面标题
 * @param {string} title - 页面标题
 * @param {boolean} appendSiteName - 是否追加站点名称
 */
function setTitle(title, appendSiteName = true) {
  if (!title) return;

  const fullTitle =
    appendSiteName && !title.includes("Echo Lab")
      ? `${title} - Echo Lab`
      : title;

  document.title = fullTitle;
}

/**
 * 设置或更新meta标签
 * @param {string} name - meta标签的name或property
 * @param {string} content - meta标签的content
 * @param {string} type - 标签类型：'name' 或 'property'
 */
function setMeta(name, content, type = "name") {
  if (!name || !content) return;

  // 查找现有的meta标签
  let meta = document.querySelector(`meta[${type}="${name}"]`);

  if (meta) {
    // 更新现有标签
    meta.setAttribute("content", content);
  } else {
    // 创建新标签
    meta = document.createElement("meta");
    meta.setAttribute(type, name);
    meta.setAttribute("content", content);
    document.head.appendChild(meta);
  }
}

/**
 * 设置结构化数据
 * @param {Object} data - 结构化数据对象
 */
function setStructuredData(data) {
  if (!data) return;

  // 移除现有的结构化数据
  const existingScript = document.querySelector(
    'script[type="application/ld+json"]'
  );
  if (existingScript) {
    existingScript.remove();
  }

  // 添加新的结构化数据
  const script = document.createElement("script");
  script.type = "application/ld+json";
  script.textContent = JSON.stringify(data);
  document.head.appendChild(script);
}

/**
 * 生成网站基础结构化数据
 */
function getWebsiteStructuredData() {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: "Echo Lab",
    description: "专业的日语精听练习工具",
    url: "https://echolab.club",
    potentialAction: {
      "@type": "SearchAction",
      target: "https://echolab.club/?search={search_term_string}",
      "query-input": "required name=search_term_string",
    },
  };
}

/**
 * 生成视频内容结构化数据
 * @param {Object} content - 内容对象
 */
function getVideoStructuredData(content) {
  if (!content) return null;

  return {
    "@context": "https://schema.org",
    "@type": "VideoObject",
    name: content.name,
    description: content.description || "Echo Lab 精听练习视频",
    thumbnailUrl: content.thumbnailUrl || "/icons/logo-512.png",
    uploadDate: content.createdAt,
    duration: "PT5M", // 默认5分钟，实际应该从内容中获取
    contentUrl: `https://echolab.club/player/${content.id}`,
    embedUrl: `https://echolab.club/player/${content.id}`,
    publisher: {
      "@type": "Organization",
      name: "Echo Lab",
      logo: {
        "@type": "ImageObject",
        url: "https://echolab.club/icons/logo-512.png",
      },
    },
    educationalUse: "语言学习",
    learningResourceType: "听力练习",
    inLanguage: "ja",
  };
}

/**
 * 设置规范链接标签
 * @param {string} url - 规范URL
 */
function setCanonical(url) {
  if (!url) return;

  // 删除已存在的canonical标签
  const existingCanonical = document.querySelector('link[rel="canonical"]');
  if (existingCanonical) {
    existingCanonical.remove();
  }

  // 创建新的canonical标签
  const link = document.createElement("link");
  link.setAttribute("rel", "canonical");
  link.setAttribute("href", url);
  document.head.appendChild(link);
}

/**
 * 主要的SEO管理函数
 * @param {Object} seoConfig - SEO配置对象
 */
export function useSEO(seoConfig = {}) {
  const route = useRoute();
  const currentSEO = ref({ ...DEFAULT_SEO, ...seoConfig });

  // 保存原始状态
  function saveOriginalState() {
    if (!originalTitle) {
      originalTitle = document.title;
    }

    // 保存原始meta标签
    const metaTags = document.querySelectorAll("meta[name], meta[property]");
    metaTags.forEach((meta) => {
      const key = meta.getAttribute("name") || meta.getAttribute("property");
      if (key && !originalMeta.has(key)) {
        originalMeta.set(key, meta.getAttribute("content"));
      }
    });
  }

  // 恢复原始状态
  function restoreOriginalState() {
    if (originalTitle) {
      document.title = originalTitle;
    }

    // 恢复原始meta标签
    originalMeta.forEach((content, key) => {
      setMeta(key, content);
    });
  }

  // 应用SEO设置
  function applySEO() {
    const seo = currentSEO.value;

    // 设置标题
    setTitle(seo.title);

    // 设置基础meta标签
    setMeta("description", seo.description);
    setMeta("keywords", seo.keywords);

    // 设置Open Graph标签
    setMeta("og:title", seo.title, "property");
    setMeta("og:description", seo.description, "property");
    setMeta("og:image", seo.image, "property");
    setMeta("og:url", seo.url, "property");
    setMeta("og:type", seo.type, "property");
    setMeta("og:site_name", "Echo Lab", "property");

    // 设置Twitter Card标签
    setMeta("twitter:card", "summary_large_image", "name");
    setMeta("twitter:title", seo.title, "name");
    setMeta("twitter:description", seo.description, "name");
    setMeta("twitter:image", seo.image, "name");

    // 设置规范链接
    setCanonical(seo.url || `https://echolab.club${route.path}`);

    // 设置结构化数据
    if (seo.structuredData) {
      setStructuredData(seo.structuredData);
    } else {
      // 默认设置网站结构化数据
      setStructuredData(getWebsiteStructuredData());
    }
  }

  // 更新SEO配置
  function updateSEO(newConfig) {
    currentSEO.value = { ...currentSEO.value, ...newConfig };
    applySEO();
  }

  // 设置视频内容SEO
  function setVideoSEO(content) {
    if (!content) return;

    const videoSEO = {
      title: content.name,
      description:
        content.description || `观看 ${content.name} - Echo Lab 精听练习视频`,
      url: `https://echolab.club/player/${content.id}`,
      image: content.thumbnailUrl || DEFAULT_SEO.image,
      type: "video.other",
      structuredData: getVideoStructuredData(content),
    };

    updateSEO(videoSEO);
  }

  // 监听路由变化
  watch(
    () => route.path,
    () => {
      // 路由变化时重新应用SEO设置
      applySEO();
    }
  );

  // 组件挂载时应用SEO设置
  onMounted(() => {
    saveOriginalState();
    applySEO();
  });

  // 组件卸载时恢复原始状态
  onUnmounted(() => {
    restoreOriginalState();
  });

  return {
    currentSEO,
    updateSEO,
    setVideoSEO,
    setTitle,
    setMeta,
    setStructuredData,
  };
}

// 页面特定的SEO配置
export const PAGE_SEO_CONFIG = {
  home: {
    title: "Echo Lab - 日语精听工具",
    description:
      "Echo Lab 是一个高效的精听学习工具，帮助你通过重复听力快速提升语言理解力。支持日语、中文、英语多语言学习。",
    keywords:
      "日语学习,精听训练,听力练习,语言学习,日本语,N2,N3,N4,N5,中文学习,英语学习",
  },

  player: {
    title: "视频播放器",
    description:
      "在线观看精听练习视频，支持重复播放、语速调节、字幕显示等功能。",
    keywords: "视频播放,精听练习,语言学习,听力训练",
  },

  contentInfo: {
    title: "关于视频内容",
    description: "了解 Echo Lab 视频内容的制作方式、适用人群和使用建议。",
    keywords: "Echo Lab,视频内容,使用说明,学习指南",
  },

  installGuide: {
    title: "安装指南",
    description: "学习如何将 Echo Lab 添加到桌面或主屏幕，获得更好的使用体验。",
    keywords: "PWA安装,桌面应用,移动应用,安装指南",
  },

  login: {
    title: "用户登录",
    description: "登录 Echo Lab 账户，享受更多个性化功能和内容管理服务。",
    keywords: "用户登录,账户管理,邮箱验证",
  },

  editor: {
    title: "内容编辑器",
    description: "使用 Echo Lab 强大的节点式编辑器创建和编辑精听练习内容。",
    keywords: "内容编辑,节点编辑器,视频制作,内容创建",
  },

  content: {
    title: "内容管理",
    description: "管理您创建的所有精听练习内容，支持编辑、发布、删除等操作。",
    keywords: "内容管理,视频管理,素材管理",
  },
};
