<template>
  <div class="input-test-container">
    <h2>输入框测试</h2>
    
    <div class="input-group">
      <label>普通输入框</label>
      <input type="text" v-model="inputValue" placeholder="请输入内容" />
    </div>
    
    <div class="input-group">
      <label>Element Plus 输入框</label>
      <el-input v-model="elInputValue" placeholder="请输入内容"></el-input>
    </div>
    
    <div class="values">
      <p>普通输入框值: {{ inputValue }}</p>
      <p>Element Plus 输入框值: {{ elInputValue }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const inputValue = ref('');
const elInputValue = ref('');
</script>

<style scoped>
.input-test-container {
  max-width: 31.25rem;
  margin: 3.125rem auto;
  padding: 1.25rem;
  border: 0.0625rem solid #eee;
  border-radius: 0.3125rem;
}

.input-group {
  margin-bottom: 1.25rem;
}

.input-group label {
  display: block;
  margin-bottom: 0.3125rem;
}

input {
  width: 100%;
  padding: 0.625rem;
  border: 0.0625rem solid #ddd;
  border-radius: 0.25rem;
}

.values {
  margin-top: 1.875rem;
  padding-top: 1.25rem;
  border-top: 0.0625rem solid #eee;
}
</style>
