<!--
  公开合集页面
  展示所有公开的合集
-->
<template>
  <div class="collections-page" :class="{ 'mobile-device': isMobile }">
    <!-- 页面标题栏 -->
    <SmartPageHeader title="公开合集" :force-show-back="true" />

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <p>发现精彩的听力练习合集</p>
      </div>
    </div>

    <!-- 合集列表 -->
    <div class="collections-container">
      <CollectionGrid :collections="collectionStore.publicCollections" :loading="collectionStore.loading && isFirstLoad"
        :show-actions="false" :show-author="true" :show-view-count="true" :show-favorite-count="true"
        :show-favorite-button="true" :show-pagination="false" empty-text="暂无公开合集"
        @toggle-favorite="handleToggleFavorite" />

      <!-- 加载更多按钮 -->
      <div v-if="!isFirstLoad.value && hasMore" class="load-more-container">
        <el-button type="primary" size="large" :loading="collectionStore.loading" @click="loadMore">
          <span v-if="!collectionStore.loading">加载更多</span>
          <span v-else>加载中...</span>
        </el-button>
      </div>

      <!-- 没有更多数据提示 -->
      <div v-if="!isFirstLoad.value && !hasMore && collectionStore.publicCollections.length > 0" class="no-more-tip">
        <el-divider>
          <span class="no-more-text">已显示全部合集</span>
        </el-divider>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useCollectionStore } from '@/stores/collectionStore';
import { useCollectionFavoriteStore } from '@/stores/collectionFavoriteStore';
import { useUserStore } from '@/stores/userStore';
import SmartPageHeader from '@/components/common/SmartPageHeader.vue';
import CollectionGrid from '@/components/collection/CollectionGrid.vue';
import { ElMessage } from 'element-plus';
import { isMobileDevice } from '@/utils/deviceDetector';

const router = useRouter();
const collectionStore = useCollectionStore();
const collectionFavoriteStore = useCollectionFavoriteStore();
const userStore = useUserStore();

// 移动端检测
const isMobile = ref(isMobileDevice());

// 加载更多相关状态
const currentPage = ref(1);
const pageSize = 12; // 每页加载12个合集

// 计算是否还有更多数据
const hasMore = computed(() => {
  const pagination = collectionStore.publicPagination;
  if (!pagination) return false;
  return currentPage.value < pagination.totalPages;
});

// 计算是否为首次加载
const isFirstLoad = computed(() => currentPage.value === 1 && collectionStore.loading);

// 处理合集收藏切换
async function handleToggleFavorite(collection) {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录后再收藏合集');
    router.push('/login');
    return;
  }

  try {
    const result = await collectionFavoriteStore.toggleFavorite(collection.id, collection);

    if (result.success) {
      if (result.isFavorited) {
        ElMessage.success('收藏成功');
      } else {
        ElMessage.success('取消收藏成功');
      }
    } else {
      throw new Error(result.error || '收藏操作失败');
    }
  } catch (error) {
    console.error('收藏操作失败:', error);
    ElMessage.error(error.message || '收藏操作失败');
  }
}

// 加载更多数据
async function loadMore() {
  if (collectionStore.loading || !hasMore.value) return;

  currentPage.value += 1;

  try {
    // 加载下一页数据，append模式
    await collectionStore.fetchPublicCollections({
      page: currentPage.value,
      limit: pageSize,
      append: true // 追加模式，不清空现有数据
    });
  } catch (error) {
    console.error('加载更多合集失败:', error);
    ElMessage.error('加载失败，请稍后重试');
    // 回退页码
    currentPage.value -= 1;
  }
}

// 组件挂载时加载数据
onMounted(async () => {
  // 重置状态
  currentPage.value = 1;

  // 加载第一页公开合集数据
  await collectionStore.fetchPublicCollections({
    page: 1,
    limit: pageSize,
    append: false // 首次加载，清空现有数据
  });

  // 如果用户已登录，加载收藏状态
  if (userStore.isLoggedIn) {
    try {
      await collectionFavoriteStore.fetchFavoriteCollections();
    } catch (error) {
      console.error('加载收藏状态失败:', error);
    }
  }
});
</script>

<style scoped>
.collections-page {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.page-header {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  padding: 2.5rem 1rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(79, 70, 229, 0.15);
}

/* 移动端页面头部优化 */
.mobile-device .page-header {
  padding: 1.5rem 0.75rem;
}

.header-content p {
  font-size: 1.125rem;
  margin: 0;
  opacity: 0.9;
}

.collections-container {
  width: 100%;
  padding: 2rem 1rem;
  box-sizing: border-box;
}

/* 移动端优化 */
.mobile-device .collections-container {
  padding: 1rem 0.75rem;
}

/* 加载更多按钮 */
.load-more-container {
  text-align: center;
  margin-top: 1.5rem;
  padding: 1rem;
}

.load-more-container .el-button {
  min-width: 8rem;
  padding: 0.75rem 2rem;
  border-radius: 2rem;
  box-shadow: 0 0.25rem 0.75rem rgba(79, 70, 229, 0.2);
}

/* 没有更多数据提示 */
.no-more-tip {
  margin-top: 2rem;
  text-align: center;
}

.no-more-text {
  color: #909399;
  font-size: 0.875rem;
}
</style>
