<!--
  合集编辑页面
  用于编辑合集信息和管理合集内容
-->
<template>
  <div class="collection-edit">
    <!-- 页面标题栏 -->
    <SmartPageHeader title="编辑合集" :force-show-back="true">
      <template #actions>
        <el-tag v-if="hasUnsavedChanges" type="warning" size="small" style="margin-right: 8px">
          有未保存的更改
        </el-tag>
        <el-button @click="saveCollection" type="primary" :loading="saving" size="small">
          保存
        </el-button>
      </template>
    </SmartPageHeader>

    <div class="edit-content" v-if="collection">
      <!-- 合集基本信息 -->
      <el-card class="info-card">
        <template #header>
          <span>合集信息</span>
        </template>

        <el-form :model="collectionForm" label-width="5rem"> <!-- 80px ÷ 16 -->
          <el-form-item label="合集名称" required>
            <el-input v-model="collectionForm.name" placeholder="请输入合集名称" />
          </el-form-item>

          <el-form-item label="合集描述">
            <el-input v-model="collectionForm.description" type="textarea" :rows="3" placeholder="请输入合集描述" />
          </el-form-item>

          <el-form-item label="封面图片">
            <el-input v-model="collectionForm.coverImageUrl" placeholder="请输入封面图片URL" />
          </el-form-item>

          <el-form-item label="标签">
            <el-input v-model="collectionForm.tags" placeholder="请输入标签，用逗号分隔" />
          </el-form-item>

          <el-form-item label="公开设置">
            <el-switch v-model="collectionForm.isPublic" active-text="公开" inactive-text="私有" />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 合集内容管理 -->
      <el-card class="content-card">
        <template #header>
          <div class="content-header">
            <span>合集内容 ({{ collectionItems.length }})</span>
            <el-button @click="showAddContentDialog" type="primary" size="small">
              <el-icon>
                <i-ep-plus />
              </el-icon>
              添加内容
            </el-button>
          </div>
        </template>

        <!-- 合集内容列表 -->
        <div v-if="collectionItems.length > 0" class="content-list">
          <draggable v-model="collectionItems" @end="handleDragEnd" item-key="id" class="draggable-list" handle=".drag-handle">
            <template #item="{ element, index }">
              <div class="content-item" :class="{ 'item-new': element.isNew }">
                <div class="drag-handle" title="拖拽排序">
                  <el-icon>
                    <i-ep-rank />
                  </el-icon>
                </div>
                <div class="item-index">{{ index + 1 }}</div>
                <div class="item-thumbnail">
                  <ResponsiveImage v-if="element.content?.thumbnailUrl" :src="element.content.thumbnailUrl" alt="缩略图" />
                  <div v-else class="thumbnail-placeholder">
                    <el-icon>
                      <i-ep-video-play />
                    </el-icon>
                  </div>
                </div>
                <div class="item-info">
                  <h4 class="item-title">
                    {{ element.content?.name || '未知内容' }}
                    <el-tag v-if="element.isNew" type="success" size="small" style="margin-left: 8px">新添加</el-tag>
                  </h4>
                  <p class="item-description">{{ element.content?.description || '暂无描述' }}</p>
                </div>
                <div class="item-actions">
                  <el-button @click="removeContent(element)" type="danger" size="small" title="移除">
                    <el-icon>
                      <i-ep-delete />
                    </el-icon>
                  </el-button>
                </div>
              </div>
            </template>
          </draggable>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-content">
          <el-empty description="暂无内容，点击上方按钮添加内容" :image-size="100">
            <el-button @click="showAddContentDialog" type="primary">
              添加第一个内容
            </el-button>
          </el-empty>
        </div>
      </el-card>
    </div>

    <!-- 加载状态 -->
    <div v-else class="loading-state">
      <el-skeleton :rows="5" animated />
    </div>

    <!-- 添加内容对话框 -->
    <StandardDialog v-model="addContentDialogVisible" title="添加内容到合集" width="80%">
      <AddContentToCollection :collection-id="collectionId" :existing-content-ids="existingContentIds"
        @content-added="handleContentAdded" @close="addContentDialogVisible = false" />
    </StandardDialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import draggable from 'vuedraggable';
import { useCollectionStore } from '@/stores/collectionStore';
import collectionService from '@/services/collectionService';
import SmartPageHeader from '@/components/common/SmartPageHeader.vue';
import AddContentToCollection from '@/components/collection/AddContentToCollection.vue';
import ResponsiveImage from '@/components/common/ResponsiveImage.vue';
import StandardDialog from '@/components/common/StandardDialog.vue';

const route = useRoute();
const router = useRouter();
const collectionStore = useCollectionStore();

// 响应式数据
const collection = ref(null);
const collectionItems = ref([]);
const saving = ref(false);
const addContentDialogVisible = ref(false);

// 变更追踪
const pendingChanges = ref({
  addedItems: [], // 新添加的内容ID
  removedItems: [], // 待移除的内容ID
  reordered: false // 是否重新排序
});

// 合集ID
const collectionId = route.params.id;

// 合集表单数据
const collectionForm = ref({
  name: '',
  description: '',
  coverImageUrl: '',
  tags: '',
  isPublic: false,
});

// 计算属性
const existingContentIds = computed(() => {
  return collectionItems.value.map(item => item.contentId);
});

// 检查是否有未保存的更改
const hasUnsavedChanges = computed(() => {
  return pendingChanges.value.addedItems.length > 0 || 
         pendingChanges.value.removedItems.length > 0 || 
         pendingChanges.value.reordered;
});



// 加载合集数据
const loadCollection = async () => {
  try {
    const response = await collectionService.getCollectionById(collectionId);
    if (response.success) {
      collection.value = response.collection;

      // 填充表单数据
      collectionForm.value = {
        name: collection.value.name || '',
        description: collection.value.description || '',
        coverImageUrl: collection.value.coverImageUrl || '',
        tags: collection.value.tags || '',
        isPublic: collection.value.isPublic || false,
      };

      // 设置合集内容，按排序顺序
      collectionItems.value = (collection.value.items || [])
        .sort((a, b) => a.sortOrder - b.sortOrder);
    } else {
      throw new Error(response.error || '获取合集信息失败');
    }
  } catch (error) {
    console.error('加载合集失败:', error);
    ElMessage.error(error.message || '加载合集失败');
  }
};

// 保存合集
const saveCollection = async () => {
  if (!collectionForm.value.name.trim()) {
    ElMessage.warning('请输入合集名称');
    return;
  }

  saving.value = true;
  try {
    // 1. 保存基本信息
    await collectionService.updateCollection(collectionId, collectionForm.value);

    // 2. 处理内容变更
    let hasContentChanges = false;
    
    // 添加新内容
    if (pendingChanges.value.addedItems.length > 0) {
      try {
        await collectionService.addContentToCollection(collectionId, pendingChanges.value.addedItems);
        hasContentChanges = true;
      } catch (error) {
        console.error('批量添加内容失败:', error);
        ElMessage.warning('添加内容失败');
      }
    }
    
    // 移除内容
    if (pendingChanges.value.removedItems.length > 0) {
      for (const contentId of pendingChanges.value.removedItems) {
        try {
          await collectionService.removeContentFromCollection(collectionId, contentId);
          hasContentChanges = true;
        } catch (error) {
          console.error(`移除内容 ${contentId} 失败:`, error);
        }
      }
    }

    // 3. 更新排序（如果有变更或重新排序）
    if (collectionItems.value.length > 0 && (pendingChanges.value.reordered || hasContentChanges)) {
      const itemOrders = collectionItems.value
        .filter(item => !item.isNew || pendingChanges.value.addedItems.includes(item.contentId))
        .map((item, index) => ({
          contentId: item.contentId,
          sortOrder: index + 1,
        }));

      if (itemOrders.length > 0) {
        await collectionService.updateCollectionItemOrder(collectionId, itemOrders);
      }
    }

    // 4. 清空待处理变更
    pendingChanges.value = {
      addedItems: [],
      removedItems: [],
      reordered: false
    };

    ElMessage.success('保存成功');

    // 5. 重新加载数据以确保同步
    await loadCollection();
    
    // 6. 刷新store中的数据
    collectionStore.fetchUserCollections();
  } catch (error) {
    console.error('保存合集失败:', error);
    ElMessage.error(error.message || '保存合集失败');
  } finally {
    saving.value = false;
  }
};

// 显示添加内容对话框
const showAddContentDialog = () => {
  addContentDialogVisible.value = true;
};

// 处理内容添加
const handleContentAdded = (addedContents) => {
  // 添加到本地列表，不直接调用接口
  addedContents.forEach(content => {
    const newItem = {
      id: `temp_${Date.now()}_${Math.random()}`, // 临时ID
      contentId: content.id,
      content: content,
      sortOrder: collectionItems.value.length + 1,
      isNew: true // 标记为新添加
    };
    collectionItems.value.push(newItem);
    pendingChanges.value.addedItems.push(content.id);
  });
  
  ElMessage.success(`已添加 ${addedContents.length} 个内容，请保存以确认更改`);
};

// 移除内容
const removeContent = async (item) => {
  try {
    await ElMessageBox.confirm(
      `确定要从合集中移除 "${item.content?.name}" 吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    // 从本地列表中移除
    const index = collectionItems.value.findIndex(i => i.id === item.id);
    if (index !== -1) {
      collectionItems.value.splice(index, 1);
      
      // 如果是新添加的内容，从待添加列表中移除
      if (item.isNew) {
        const addedIndex = pendingChanges.value.addedItems.indexOf(item.contentId);
        if (addedIndex !== -1) {
          pendingChanges.value.addedItems.splice(addedIndex, 1);
        }
      } else {
        // 如果是原有内容，添加到待移除列表
        pendingChanges.value.removedItems.push(item.contentId);
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除内容失败:', error);
    }
  }
};

// 处理拖拽结束
const handleDragEnd = () => {
  // 标记为已重新排序
  pendingChanges.value.reordered = true;
  console.log('拖拽排序已更改，将在保存时生效');
};

// 初始化
onMounted(() => {
  if (collectionId) {
    loadCollection();
  }
});
</script>

<style scoped>
.collection-edit {
  width: 100%;
  padding: 0;
}

.edit-content {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.info-card,
.content-card {
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1);
  /* 2px, 8px ÷ 16 */
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.content-list {
  max-height: 37.5rem;
  /* 600px ÷ 16 */
  overflow-y: auto;
}

.draggable-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.content-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 0.0625rem solid #e4e7ed;
  /* 1px ÷ 16 */
  border-radius: 0.5rem;
  /* 8px ÷ 16 */
  background: #fafafa;
  transition: all 0.3s ease;
}

.content-item.item-new {
  border-color: #67c23a;
  background: #f0f9ff;
}



.drag-handle {
  cursor: move;
  color: #909399;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.drag-handle:hover {
  color: #409eff;
  background: rgba(64, 158, 255, 0.1);
}

.content-item:hover {
  background: #f0f9ff;
  border-color: #409eff;
}

.item-index {
  width: 1.875rem;
  /* 30px ÷ 16 */
  height: 1.875rem;
  /* 30px ÷ 16 */
  border-radius: 50%;
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

.item-thumbnail {
  position: relative;
  width: 5rem;
  /* 80px ÷ 16 */
  height: 2.8125rem;
  /* 45px ÷ 16 */
  border-radius: 0.25rem;
  /* 4px ÷ 16 */
  overflow: hidden;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
}



.thumbnail-placeholder {
  color: #909399;
}

.item-info {
  flex: 1;
  min-width: 0;
}

.item-title {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-description {
  margin: 0;
  font-size: 0.875rem;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-actions {
  display: flex;
  gap: 0.5rem;
}

.empty-content {
  padding: 3rem 1rem;
  text-align: center;
}

.loading-state {
  padding: 2rem;
}

/* 使用rem单位，移除媒体查询 */
</style>
