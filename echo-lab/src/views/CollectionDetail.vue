<!--
  合集详情页面
  展示合集信息和内容列表，支持播放
-->
<template>
  <div class="collection-detail" :class="{ 'mobile-layout': isMobile }">
    <!-- 页面标题栏 -->
    <SmartPageHeader :title="collection?.name || '合集详情'" :force-show-back="true">
      <template #actions>
        <el-button v-if="canEdit" @click="editCollection" type="default" size="small">
          <el-icon>
            <i-ep-edit />
          </el-icon>
          编辑
        </el-button>
        <el-tooltip v-if="collection?.isPublic && !isOwner" :content="isFavorited ? '取消收藏' : '收藏'" placement="bottom">
          <el-button @click="toggleFavorite" :type="isFavorited ? 'danger' : 'default'" circle size="small"
            :loading="favoriteLoading">
            <el-icon>
              <i-ep-star-filled v-if="isFavorited" />
              <i-ep-star v-else />
            </el-icon>
          </el-button>
        </el-tooltip>
      </template>
    </SmartPageHeader>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <el-skeleton :rows="5" animated />
    </div>

    <!-- 合集信息 -->
    <div v-else-if="collection" class="collection-info">
      <!-- 合集头部 -->
      <div class="collection-header">
        <div class="header-cover">
          <ResponsiveImage v-if="collection.coverImageUrl" :src="collection.coverImageUrl" alt="合集封面" loading="lazy" />
          <div v-else class="cover-placeholder">
            <el-icon :size="64">
              <i-ep-collection />
            </el-icon>
          </div>
        </div>

        <div class="header-content">
          <h1 class="collection-title">{{ collection.name }}</h1>
          <p v-if="collection.description" class="collection-description">{{ collection.description }}</p>

          <!-- 合集元信息 -->
          <div class="collection-meta">
            <div class="meta-item">
              <el-icon>
                <i-ep-document />
              </el-icon>
              <span>{{ collection.itemCount || 0 }} 个内容</span>
            </div>
            <div class="meta-item">
              <el-icon>
                <i-ep-view />
              </el-icon>
              <span>{{ collection.viewCount || 0 }} 次浏览</span>
            </div>
            <div v-if="collection.isPublic" class="meta-item">
              <el-icon>
                <i-ep-star />
              </el-icon>
              <span>{{ collection.favoriteCount || 0 }} 次收藏</span>
            </div>
            <div class="meta-item">
              <el-tag :type="collection.status === 'published' ? 'success' : 'warning'" size="small">
                {{ collection.status === 'published' ? '已发布' : '草稿' }}
              </el-tag>
            </div>
            <div v-if="collection.isPublic" class="meta-item">
              <el-tag type="info" size="small">公开</el-tag>
            </div>
          </div>

          <!-- 作者信息 -->
          <div v-if="collection.creator" class="collection-author">
            <span class="author-name" @click="goToUserSpace(collection.creator.id)">
              {{ getAuthorDisplayName(collection.creator) }}
            </span>
          </div>

          <!-- 标签 -->
          <div v-if="collection.tags" class="collection-tags">
            <el-tag v-for="tag in parseTags(collection.tags)" :key="tag" size="small" class="tag-item">
              {{ tag }}
            </el-tag>
          </div>

          <!-- 操作按钮 -->
          <div class="collection-actions">
            <div v-if="collectionItems.length > 0">
              <el-button @click="playCollection" type="primary" size="large" class="play-button">
                <el-icon>
                  <i-ep-video-play />
                </el-icon>
                开始播放
                <span class="item-count">({{ collectionItems.length }}个内容)</span>
              </el-button>
              

            </div>
            

          </div>
        </div>
      </div>

      <!-- 内容列表 -->
      <div class="content-section">
        <h2 class="section-title">合集内容</h2>

        <div v-if="collectionItems.length > 0" class="content-list">
          <div class="list-header">
            <div class="list-stats">
              <span class="total-count">共 {{ collectionItems.length }} 个内容</span>
            </div>
            <div class="list-actions">
              <el-button size="small" @click="playAll">
                <el-icon>
                  <i-ep-video-play />
                </el-icon>
                顺序播放
              </el-button>
              <el-button size="small" @click="shufflePlay">
                <el-icon>
                  <i-ep-refresh />
                </el-icon>
                随机播放
              </el-button>
            </div>
          </div>

          <div v-for="(item, index) in collectionItems" :key="item.id" class="content-item"
            :class="{ 'is-playing': currentPlayingIndex === index }" @click="playFromIndex(index)">
            <div class="item-index">
              <span v-if="currentPlayingIndex !== index">{{ index + 1 }}</span>
              <el-icon v-else class="playing-icon">
                <i-ep-video-play />
              </el-icon>
            </div>
            <div class="item-thumbnail">
              <ResponsiveImage v-if="item.content?.thumbnailUrl" :src="item.content.thumbnailUrl" alt="缩略图" loading="lazy" />
              <div v-else class="thumbnail-placeholder">
                <el-icon>
                  <i-ep-video-play />
                </el-icon>
              </div>
              <div class="play-overlay">
                <el-icon :size="24">
                  <i-ep-video-play />
                </el-icon>
              </div>
            </div>
            <div class="item-info">
              <h3 class="item-title">{{ item.content?.name || '未知内容' }}</h3>
              <p class="item-description">{{ item.content?.description || '暂无描述' }}</p>
              <div class="item-meta">
                <span class="item-date">{{ formatDate(item.content?.updatedAt) }}</span>
                <span class="item-tags" v-if="item.content?.tags">
                  <el-tag v-for="tag in parseTags(item.content.tags)" :key="tag" size="small" class="tag-item">
                    {{ tag }}
                  </el-tag>
                </span>
              </div>
            </div>
            <div class="item-actions">
              <el-tooltip content="收藏内容" placement="top">
                <FavoriteButton :content-id="item.content?.id" :content-data="item.content" size="small" @click.stop />
              </el-tooltip>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-content">
          <el-empty description="合集暂无内容" :image-size="100" />
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-state">
      <el-empty description="合集不可用" :image-size="120">
        <template #description>
          <div class="error-description">
            <h3>合集不可用</h3>
            <p>该合集可能已被删除、下架或您没有访问权限</p>
          </div>
        </template>
        <div class="error-actions">
          <el-button type="primary" @click="router.push('/')">
            <el-icon>
              <i-ep-house />
            </el-icon>
            返回首页
          </el-button>
          <el-button @click="goBack">
            <el-icon>
              <i-ep-arrow-left />
            </el-icon>
            返回上页
          </el-button>
        </div>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
// 图标通过 unplugin-icons 自动导入，无需手动导入
import { useUserStore } from '@/stores/userStore';
import { useCollectionFavoriteStore } from '@/stores/collectionFavoriteStore';
import collectionService from '@/services/collectionService';
import SmartPageHeader from '@/components/common/SmartPageHeader.vue';
import FavoriteButton from '@/components/common/FavoriteButton.vue';
import { isMobileDevice } from '@/utils/deviceDetector';
import ResponsiveImage from '@/components/common/ResponsiveImage.vue';

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const collectionFavoriteStore = useCollectionFavoriteStore();

// 响应式数据
const loading = ref(true);
const favoriteLoading = ref(false);
const collection = ref(null);
const collectionItems = ref([]);
const currentPlayingIndex = ref(-1); // 当前播放的内容索引

// 合集ID
const collectionId = route.params.id;

// 计算属性
const isOwner = computed(() => {
  return userStore.user && collection.value && collection.value.userId === userStore.user.id;
});

const canEdit = computed(() => {
  return isOwner.value;
});

// 从 store 获取收藏状态
const isFavorited = computed(() => {
  return collection.value ? collectionFavoriteStore.isFavorite(collection.value.id) : false;
});

// 设备检测
const isMobile = computed(() => isMobileDevice());

// 移除总时长计算，因为数据中没有duration字段

// 加载合集数据
const loadCollection = async () => {
  loading.value = true;
  try {
    const response = await collectionService.getCollectionById(collectionId);
    if (response.success) {
      collection.value = response.collection;

      // 设置合集内容，按排序顺序
      collectionItems.value = (collection.value.items || [])
        .sort((a, b) => a.sortOrder - b.sortOrder);
    } else {
      throw new Error(response.error || '获取合集信息失败');
    }
  } catch (error) {
    console.error('加载合集失败:', error);

    // 根据错误状态码提供更友好的提示
    if (error.response?.status === 404) {
      ElMessage({
        message: '当前合集不可用，可能已被删除或下架',
        type: 'error',
        duration: 0,
        showClose: true
      });
    } else if (error.response?.status === 403) {
      ElMessage({
        message: '您没有权限访问此合集',
        type: 'error',
        duration: 0,
        showClose: true
      });
    } else {
      ElMessage.error(error.message || '加载合集失败');
    }
  } finally {
    loading.value = false;
  }
};

// 播放合集（默认顺序播放）
const playCollection = () => playFromIndex(0, 'sequence');

// 顺序播放全部
const playAll = () => playFromIndex(0, 'sequence');

// 随机播放
const shufflePlay = () => {
  if (collectionItems.value.length === 0) return;
  const randomIndex = Math.floor(Math.random() * collectionItems.value.length);
  playFromIndex(randomIndex, 'random');
};

// 从指定索引开始播放
const playFromIndex = (index, playMode = 'sequence') => {
  if (index < 0 || index >= collectionItems.value.length) {
    console.warn('无效的播放索引:', index);
    return;
  }

  const item = collectionItems.value[index];
  if (!item?.content?.id) {
    console.warn('内容数据不完整:', item);
    ElMessage.error('内容不可用');
    return;
  }

  // 设置当前播放索引
  currentPlayingIndex.value = index;

  // 简化：只传递必要参数，播放器根据contentId自动计算位置
  router.push({
    path: `/player/${item.content.id}`,
    query: {
      collection: collectionId,
      playMode: playMode
    }
  });
};

// 编辑合集
const editCollection = () => {
  router.push(`/collection/${collectionId}/edit`);
};

// 切换收藏状态
const toggleFavorite = async () => {
  if (!userStore.user) {
    ElMessage.warning('请先登录');
    return;
  }

  favoriteLoading.value = true;
  try {
    const result = await collectionFavoriteStore.toggleFavorite(collectionId, collection.value);

    if (result.success) {
      // 更新收藏数量
      if (result.isFavorited) {
        collection.value.favoriteCount = (collection.value.favoriteCount || 0) + 1;
        ElMessage.success('收藏成功');
      } else {
        collection.value.favoriteCount = Math.max((collection.value.favoriteCount || 0) - 1, 0);
        ElMessage.success('取消收藏成功');
      }
    } else {
      throw new Error(result.error || '操作失败');
    }
  } catch (error) {
    console.error('收藏操作失败:', error);
    ElMessage.error(error.message || '操作失败');
  } finally {
    favoriteLoading.value = false;
  }
};

// 跳转到用户空间
const goToUserSpace = (userId) => {
  router.push(`/user/${userId}`);
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return '';
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  } catch (error) {
    return '';
  }
};

// 解析标签字符串为数组
const parseTags = (tagsStr) => {
  if (!tagsStr) return [];
  if (Array.isArray(tagsStr)) return tagsStr;
  return tagsStr.split(',').filter(tag => tag.trim());
};

// 获取作者显示名称
const getAuthorDisplayName = (creator) => {
  if (!creator) return '未知作者';

  // 优先显示用户名
  if (creator.username && creator.username.trim()) {
    return creator.username;
  }

  // 显示用户ID
  if (creator.id) {
    return creator.id;
  }

  return '未知作者';
};



// 初始化
onMounted(() => {
  if (collectionId) {
    loadCollection();
  }
});
</script>

<style scoped>
.collection-detail {
  width: 100%;
  padding: 0;
}

.collection-info {
  max-width: 100%;
  padding: 2rem;
  box-sizing: border-box;
}

/* 手机端整体布局 */
.mobile-layout .collection-info {
  padding: 1rem;
}

.loading-state,
.error-state {
  padding: 3rem 1rem;
  text-align: center;
}

.error-description h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  color: #303133;
}

.error-description p {
  margin: 0;
  color: #606266;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 1rem;
}

.collection-header {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  border-radius: 1rem;
  box-shadow: 0 0.25rem 1rem rgba(79, 70, 229, 0.2);
  color: white;
  position: relative;
  overflow: hidden;
}

.collection-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="%23ffffff" opacity="0.15"/><circle cx="10" cy="90" r="0.5" fill="%23ffffff" opacity="0.15"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
  pointer-events: none;
}

/* 手机端合集头部 */
.mobile-layout .collection-header {
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
  padding: 1rem;
}

.header-cover {
  width: 12.5rem;
  height: 7.5rem;
  border-radius: 1rem;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border: 2px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

/* 手机端封面 */
.mobile-layout .header-cover {
  width: 100%;
  height: 10rem;
  align-self: center;
  max-width: 20rem;
}

.header-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.header-content {
  flex: 1;
  min-width: 0;
}

.collection-title {
  margin: 0 0 1rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.collection-description {
  margin: 0 0 1.5rem 0;
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
}

/* 手机端标题和描述 */
.mobile-layout .collection-title {
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
  text-align: center;
}

.mobile-layout .collection-description {
  font-size: 0.9rem;
  margin-bottom: 1rem;
  text-align: center;
}

.collection-meta {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  backdrop-filter: blur(10px);
}

/* 手机端元信息 */
.mobile-layout .collection-meta {
  justify-content: center;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.mobile-layout .meta-item {
  font-size: 0.8rem;
}

.collection-author {
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.author-label {
  color: #909399;
}

.author-name {
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  text-decoration: none;
  font-weight: 500;
}

.author-name:hover {
  text-decoration: underline;
}

.collection-tags {
  margin-bottom: 1.5rem;
}

/* 手机端作者和标签 */
.mobile-layout .collection-author {
  text-align: center;
  margin-bottom: 0.75rem;
  font-size: 0.8rem;
}

.mobile-layout .collection-tags {
  text-align: center;
  margin-bottom: 1rem;
}

.tag-item {
  margin-right: 0.5rem;
  margin-bottom: 0.25rem;
}

.collection-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

/* 手机端操作按钮 */
.mobile-layout .collection-actions {
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  margin-top: 1rem;
}

.play-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-size: 1.125rem;
}

/* 手机端播放按钮 */
.mobile-layout .play-button {
  width: 100%;
  max-width: 20rem;
  justify-content: center;
  font-size: 1rem;
  padding: 0.875rem 1rem;
}

.play-button .el-icon {
  font-size: 1.25rem;
}

.item-count {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  margin-left: 0.25rem;
}

.play-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* 收藏按钮样式 */
.collection-actions .el-button.is-favorited {
  background-color: #f56c6c;
  color: #ffffff;
  border-color: #f56c6c;
}

/* 统一收藏按钮图标大小 */
.collection-actions .el-button .el-icon {
  font-size: 1.125rem !important;
  width: 1.125rem;
  height: 1.125rem;
}

.content-section {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.section-title {
  margin: 0;
  padding: 1.5rem 2rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #f0f0f0;
}

/* 手机端内容区域 */
.mobile-layout .section-title {
  padding: 1rem;
  font-size: 1.125rem;
  text-align: center;
}

.content-list {
  padding: 0.75rem;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: #f8f9fa;
  border-radius: 0.75rem;
  margin-bottom: 0.75rem;
  border: 1px solid #e9ecef;
}

/* 手机端列表头部 */
.mobile-layout .content-list {
  padding: 0.25rem;
}

.mobile-layout .list-header {
  flex-direction: column;
  gap: 0.75rem;
  padding: 0.75rem;
}

.list-stats {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.total-count {
  font-size: 0.875rem;
  color: #606266;
  font-weight: 500;
}

.list-actions {
  display: flex;
  gap: 0.5rem;
}

.content-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #f0f0f0;
  background: white;
  position: relative;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.content-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: transparent;
  transition: all 0.3s;
}

/* 手机端内容项 */
.mobile-layout .content-item {
  gap: 0.75rem;
  padding: 0.5rem;
}

.content-item:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  transform: translateY(-1px);
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.08);
  border-color: #e2e8f0;
}

.content-item:hover::before {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}



.item-index {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.875rem;
  flex-shrink: 0;
  box-shadow: 0 0.25rem 0.75rem rgba(102, 126, 234, 0.3);
}



.item-thumbnail {
  position: relative;
  width: 7.5rem;
  height: 4.25rem;
  border-radius: 0.75rem;
  overflow: hidden;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border: 2px solid #e2e8f0;
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
}



/* 手机端缩略图 */
.mobile-layout .item-thumbnail {
  width: 5rem;
  height: 3rem;
}

.item-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  color: #909399;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.content-item:hover .play-overlay {
  opacity: 1;
}

.item-info {
  flex: 1;
  min-width: 0;
}

.item-title {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-description {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 手机端项目信息 */
.mobile-layout .item-title {
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.mobile-layout .item-description {
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
}

.item-meta {
  font-size: 0.75rem;
  color: #909399;
  display: flex;
  gap: 0.5rem;
  align-items: center;
  flex-wrap: wrap;
}

/* 手机端项目元信息 */
.mobile-layout .item-meta {
  font-size: 0.65rem;
  gap: 0.25rem;
}

.item-tags {
  display: flex;
  gap: 0.25rem;
  flex-wrap: wrap;
}

.item-tags .tag-item {
  margin: 0;
}

.playing-icon {
  color: #409eff;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.item-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  opacity: 1;
  transition: opacity 0.3s ease;
}

/* 手机端操作按钮 */
.mobile-layout .item-actions {
  opacity: 1;
  gap: 0.25rem;
}

.empty-content {
  padding: 3rem 1rem;
  text-align: center;
}

/* 手机端空内容状态 */
.mobile-layout .empty-content {
  padding: 2rem 0.5rem;
}

/* 使用rem单位，移除媒体查询 */
</style>
