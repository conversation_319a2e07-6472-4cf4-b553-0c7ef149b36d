<!--
  收藏列表页面
  显示用户收藏的内容和合集列表
-->
<template>
  <div class="favorites-page">
    <SmartPageHeader title="我的收藏" :force-show-back="true" />

    <div class="page-content">
      <!-- 标签页区域 -->
      <el-tabs v-model="activeTab" class="favorites-tabs">
        <el-tab-pane :label="`收藏的内容 (${favoriteStore.favorites.length})`" name="contents">
          <!-- 收藏的内容区域 -->
          <div class="tab-content">
            <!-- 加载状态 -->
            <div v-if="favoriteStore.loading" class="loading-container">
              <el-skeleton :rows="3" animated />
              <el-skeleton :rows="3" animated />
            </div>

            <!-- 空状态 -->
            <div v-else-if="favoriteStore.favorites.length === 0" class="empty-container">
              <el-empty description="暂无收藏内容">
                <template #description>
                  <p>您还没有收藏任何内容</p>
                </template>
                <el-button type="primary" @click="goToHome">浏览内容</el-button>
              </el-empty>
            </div>

            <!-- 内容列表 -->
            <div v-else class="favorites-grid">
              <div v-for="content in favoriteStore.favorites" :key="content.id" class="favorite-item"
                :class="{ 'unavailable': content.isAvailable === false }">
                <div class="content-wrapper">
                  <!-- 不可用状态遮罩 -->
                  <div v-if="content.isAvailable === false" class="unavailable-overlay">
                    <el-tag :type="getStatusTagType(content.unavailableReason)" size="small">
                      {{ getStatusText(content.unavailableReason) }}
                    </el-tag>
                  </div>

                  <PublicContentCard :content="content">
                    <template #actions>
                      <FavoriteButton :content-id="content.id" :content-data="content" @unfavorite="handleUnfavorite" />
                    </template>
                  </PublicContentCard>
                </div>
              </div>
            </div>


          </div>
        </el-tab-pane>

        <el-tab-pane :label="`收藏的合集 (${collectionFavoriteStore.favoriteCollections.length})`" name="collections">
          <!-- 收藏的合集区域 -->
          <div class="tab-content">
            <!-- 加载状态 -->
            <div v-if="collectionFavoriteStore.loading" class="loading-container">
              <el-skeleton :rows="3" animated />
              <el-skeleton :rows="3" animated />
            </div>

            <!-- 空状态 -->
            <div v-else-if="collectionFavoriteStore.favoriteCollections.length === 0" class="empty-container">
              <el-empty description="暂无收藏合集">
                <template #description>
                  <p>您还没有收藏任何合集</p>
                </template>
                <el-button type="primary" @click="goToCollections">查看公开合集</el-button>
              </el-empty>
            </div>

            <!-- 合集列表 -->
            <div v-else>
              <CollectionGrid :collections="collectionFavoriteStore.favoriteCollections" :loading="false"
                :show-actions="false" :show-author="true" :show-view-count="true" :show-favorite-count="true"
                :show-favorite-button="true" :show-pagination="false" empty-text="暂无收藏合集"
                @toggle-favorite="handleCollectionUnfavorite" @play="handleCollectionPlay" />
            </div>


          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useFavoriteStore } from "@/stores/favoriteStore";
import { useCollectionFavoriteStore } from "@/stores/collectionFavoriteStore";
import { useUserStore } from "@/stores/userStore";
import SmartPageHeader from "@/components/common/SmartPageHeader.vue";
import PublicContentCard from "@/components/content/PublicContentCard.vue";
import CollectionGrid from "@/components/collection/CollectionGrid.vue";
import FavoriteButton from "@/components/common/FavoriteButton.vue";

import { ElMessage } from "element-plus";

const router = useRouter();
const favoriteStore = useFavoriteStore();
const collectionFavoriteStore = useCollectionFavoriteStore();
const userStore = useUserStore();

// 移除Tab状态，改为独立区块展示

// Tab状态
const activeTab = ref('contents');

// 初始化
onMounted(async () => {
  // 检查用户是否已登录
  if (!userStore.isLoggedIn) {
    ElMessage.warning("请先登录后查看收藏内容");
    router.push("/login");
    return;
  }

  // 获取收藏列表
  await favoriteStore.fetchFavorites();

  // 获取收藏的合集列表
  await collectionFavoriteStore.fetchFavoriteCollections();
});

// 移除Tab切换处理，页面加载时直接获取所有数据

// 处理取消收藏内容
const handleUnfavorite = () => {
  // 已经在 FavoriteButton 组件中处理了状态更新
  // 这里可以添加额外的逻辑
};

// 处理取消收藏合集
const handleCollectionUnfavorite = async (collection) => {
  try {
    // 使用 toggleFavorite 方法来取消收藏
    const result = await collectionFavoriteStore.toggleFavorite(collection.id, collection);

    if (result.success) {
      if (result.isFavorited) {
        ElMessage.success('收藏成功');
      } else {
        ElMessage.success('取消收藏成功');
      }
    } else {
      throw new Error(result.error || '操作失败');
    }
  } catch (error) {
    console.error('收藏操作失败:', error);
    ElMessage.error(error.message || '收藏操作失败');
  }
};

// 处理合集播放
const handleCollectionPlay = (collection) => {
  // 检查合集是否可用
  if (collection.isAvailable === false) {
    const reasonText = getStatusText(collection.unavailableReason);
    ElMessage.warning(`该合集${reasonText}，无法播放`);
    return;
  }

  // 跳转到合集详情页面
  router.push(`/collection/${collection.id}`);
};



// 跳转到首页
const goToHome = () => {
  router.push("/");
};

// 跳转到合集列表页面
const goToCollections = () => {
  router.push("/collections");
};

// 获取状态标签类型
const getStatusTagType = (reason) => {
  switch (reason) {
    case 'deleted':
      return 'danger';
    case 'unpublished':
      return 'warning';
    case 'private':
      return 'info';
    default:
      return 'warning';
  }
};

// 获取状态文本
const getStatusText = (reason) => {
  switch (reason) {
    case 'deleted':
      return '已删除';
    case 'unpublished':
      return '已下架';
    case 'private':
      return '已私有';
    default:
      return '不可用';
  }
};
</script>

<style scoped>
.favorites-page {
  width: 100%;
}

.page-content {
  padding: 1.5rem;
}

/* 手机端样式优化 */
.mobile-layout .page-content {
  padding: 0.5rem;
}

/* Tab内容区域 */
.tab-content {
  padding-top: 1rem;
}





.loading-container,
.empty-container {
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  border: 1px solid #e9ecef;
}



.favorites-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(17.5rem, 1fr));
  gap: 1.5rem;
}

.favorite-item {
  transition: transform 0.2s ease;
}

.favorite-item:hover {
  transform: translateY(-0.125rem);
}



/* 不可用内容样式 */
.favorite-item.unavailable {
  position: relative;
  opacity: 0.6;
  filter: grayscale(0.3);
}

.favorite-item.unavailable:hover {
  opacity: 0.8;
}

.content-wrapper {
  position: relative;
}

.unavailable-overlay {
  position: absolute;
  top: 0.5rem;
  right: 3rem;
  /* 避开收藏按钮 */
  z-index: 10;
  pointer-events: none;
}

/* Tabs样式 */
.favorites-tabs {
  margin-bottom: 1rem;
}

.favorites-tabs :deep(.el-tabs__header) {
  margin-bottom: 1.5rem;
}

.favorites-tabs :deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

.favorites-tabs :deep(.el-tabs__item) {
  font-size: 1rem;
  padding: 0 1.5rem;
}

.favorites-tabs :deep(.el-tabs__item.is-active) {
  font-weight: 600;
}

/* 手机端tabs调整 */
.mobile-layout .favorites-tabs :deep(.el-tabs__item) {
  padding: 0 1rem;
  font-size: 0.9rem;
}
</style>
