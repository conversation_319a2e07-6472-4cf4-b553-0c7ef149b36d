<template>
  <div class="settings-container" :class="{ 'mobile-device': isMobile }">
    <SmartPageHeader title="设置" :force-show-back="true" />

    <div class="settings-content">
      <!-- 应用功能 -->
      <el-card class="settings-card">
        <template #header>
          <div class="card-header">
            <span>应用功能</span>
          </div>
        </template>

        <div class="function-section">
          <div class="function-list">
            <!-- 我的反馈 -->
            <div class="function-item" v-if="isLoggedIn" @click="goToFeedback">
              <div class="function-info">
                <el-icon class="function-icon">
                  <i-ep-chat-line-round />
                </el-icon>
                <div class="function-details">
                  <h4>我的反馈</h4>
                  <p>查看和管理您提交的反馈</p>
                </div>
              </div>
              <el-icon class="arrow-icon">
                <i-ep-arrow-right />
              </el-icon>
            </div>

            <!-- 添加到桌面 -->
            <div class="function-item" v-if="!pwaInstalled" @click="goToInstallGuide">
              <div class="function-info">
                <el-icon class="function-icon">
                  <i-ep-download />
                </el-icon>
                <div class="function-details">
                  <h4>{{ isMobile ? "添加到主屏幕" : "添加到桌面" }}</h4>
                  <p>将应用添加到{{ isMobile ? "主屏幕" : "桌面" }}，快速访问</p>
                </div>
              </div>
              <el-icon class="arrow-icon">
                <i-ep-arrow-right />
              </el-icon>
            </div>

            <!-- 使用帮助 -->
            <div class="function-item" @click="showHelp">
              <div class="function-info">
                <el-icon class="function-icon">
                  <i-ep-question-filled />
                </el-icon>
                <div class="function-details">
                  <h4>使用帮助</h4>
                  <p>查看应用使用指南和常见问题</p>
                </div>
              </div>
              <el-icon class="arrow-icon">
                <i-ep-arrow-right />
              </el-icon>
            </div>

            <!-- 关于 -->
            <div class="function-item" @click="goToAbout">
              <div class="function-info">
                <el-icon class="function-icon">
                  <i-ep-info-filled />
                </el-icon>
                <div class="function-details">
                  <h4>关于</h4>
                  <p>查看应用信息</p>
                </div>
              </div>
              <el-icon class="arrow-icon">
                <i-ep-arrow-right />
              </el-icon>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 系统管理 -->
      <el-card class="settings-card">
        <template #header>
          <div class="card-header">
            <span>系统管理</span>
          </div>
        </template>

        <div class="function-section">
          <div class="function-list">
            <!-- 清除缓存 -->
            <div class="function-item" @click="confirmClearCache">
              <div class="function-info">
                <el-icon class="function-icon danger">
                  <i-ep-delete />
                </el-icon>
                <div class="function-details">
                  <h4>清除所有缓存</h4>
                  <p>清除应用缓存可能有助于解决某些功能异常的问题</p>
                  <p class="warning-text">注意：将删除所有本地存储的数据，包括未保存的编辑内容</p>
                </div>
              </div>
              <el-icon class="arrow-icon" v-if="!clearing">
                <i-ep-arrow-right />
              </el-icon>
              <el-icon class="loading-icon" v-else>
                <i-ep-loading />
              </el-icon>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/userStore';
import { ElMessage, ElMessageBox } from 'element-plus';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import SmartPageHeader from '@/components/common/SmartPageHeader.vue';
import { isMobileDevice, isPWAInstalled } from '@/utils/deviceDetector';
import { useHelp } from '@/composables/useHelp';

// 路由
const router = useRouter();

// 用户状态存储
const userStore = useUserStore();

// 帮助功能
const { showHelp: showHelpDialog } = useHelp();

// 清除缓存状态
const clearing = ref(false);

// 计算属性
const isLoggedIn = computed(() => userStore.isLoggedIn);
const isMobile = computed(() => isMobileDevice());
const pwaInstalled = computed(() => isPWAInstalled());

// 导航到反馈页面
function goToFeedback() {
  router.push('/feedback');
}

// 导航到安装指南
function goToInstallGuide() {
  router.push('/install-guide');
}

// 显示帮助
function showHelp() {
  showHelpDialog('all');
}

// 导航到关于页面
function goToAbout() {
  router.push('/content-info');
}

// 确认清除缓存
function confirmClearCache() {
  ElMessageBox.confirm(
    '确定要清除所有缓存吗？这将删除所有本地存储的数据，包括未保存的编辑内容。',
    '警告',
    {
      confirmButtonText: '确定清除',
      cancelButtonText: '取消',
      type: 'warning',
      distinguishCancelAndClose: true,
    }
  )
    .then(() => {
      clearAllCache();
    })
    .catch(() => {
      // 用户取消操作
    });
}

// 清除所有缓存
async function clearAllCache() {
  clearing.value = true;

  try {
    // 清除用户状态
    userStore.logout();

    // 清除localStorage
    localStorage.clear();

    // 清除sessionStorage
    sessionStorage.clear();

    // 清除IndexedDB (如果有使用)
    const dbNames = await indexedDB.databases();
    dbNames.forEach(db => {
      indexedDB.deleteDatabase(db.name);
    });

    // 清除应用缓存 (如果支持)
    if ('caches' in window) {
      try {
        // 获取所有缓存名称
        const cacheNames = await caches.keys();
        console.log('发现以下缓存:', cacheNames);

        // 记录PWA相关缓存名称（用于日志）
        console.log('将清除PWA相关缓存，包括: aliyun-oss-audio-cache, audio-cache, workbox-precache');

        // 删除所有缓存
        await Promise.all(
          cacheNames.map(cacheName => {
            console.log(`正在删除缓存: ${cacheName}`);
            return caches.delete(cacheName);
          })
        );

        console.log('所有缓存已清除');
      } catch (cacheError) {
        console.error('清除缓存API错误:', cacheError);
      }
    }

    // 尝试清除Service Worker注册
    if ('serviceWorker' in navigator) {
      try {
        const registrations = await navigator.serviceWorker.getRegistrations();
        for (const registration of registrations) {
          await registration.unregister();
          console.log('Service Worker已注销');
        }
      } catch (swError) {
        console.error('注销Service Worker失败:', swError);
      }
    }

    // 显示成功消息
    ElMessage({
      message: '缓存已成功清除',
      type: 'success',
      duration: 3000
    });

  } catch (error) {
    console.error('清除缓存失败:', error);
    ElMessage.error('清除缓存失败，请稍后重试');
  } finally {
    clearing.value = false;
  }
}


</script>

<style scoped>
.settings-container {
  width: 100%;
}

.settings-content {
  padding: 1.5rem;
}

/* 移动端优化 */
.mobile-device .settings-content {
  padding: 0.75rem;
}

.settings-card {
  margin-bottom: 2rem;
}

/* 移动端卡片间距优化 */
.mobile-device .settings-card {
  margin-bottom: 1rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.warning-text {
  color: #E6A23C !important;
  font-weight: 500;
  font-size: 0.8rem !important;
  margin-top: 0.25rem !important;
}

/* 功能区域样式 */
.function-section {
  padding: 0;
}

.function-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.function-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
  border-bottom: 1px solid #f0f0f0;
}

/* 移动端功能项优化 */
.mobile-device .function-item {
  padding: 0.875rem;
}

.function-item:last-child {
  border-bottom: none;
}

.function-item:hover {
  background-color: #f8f9fa;
}

.function-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* 移动端功能信息优化 */
.mobile-device .function-info {
  gap: 0.75rem;
}

.function-icon {
  font-size: 1.5rem;
  color: #409eff;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 移动端图标优化 */
.mobile-device .function-icon {
  font-size: 1.25rem;
  width: 1.75rem;
  height: 1.75rem;
}

.function-icon.danger {
  color: #f56c6c;
}

.function-details h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
}

.function-details p {
  margin: 0;
  font-size: 0.875rem;
  color: #606266;
  line-height: 1.4;
}

.arrow-icon {
  font-size: 1rem;
  color: #c0c4cc;
  transition: color 0.3s ease;
}

.function-item:hover .arrow-icon {
  color: #409eff;
}

.loading-icon {
  font-size: 1rem;
  color: #409eff;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 信息显示项样式 */
.info-item {
  cursor: default !important;
}

.info-item:hover {
  background-color: transparent !important;
}

/* 移动端样式通过条件类名处理 */
</style>
