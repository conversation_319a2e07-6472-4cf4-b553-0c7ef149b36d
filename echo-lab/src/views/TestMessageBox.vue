&lt;template&gt;
&lt;div class=&quot;test-container&quot;&gt;
&lt;el-button type=&quot;primary&quot; @click=&quot;showConfirm&quot;&gt;测试 MessageBox&lt;/el-button&gt;
&lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ElMessageBox } from 'element-plus';

const showConfirm = () =&gt; {
ElMessageBox.confirm(
'这是一个测试消息',
'提示',
{
confirmButtonText: '确定',
cancelButtonText: '取消',
type: 'warning',
}
)
.then(() =&gt; {
console.log('confirmed');
})
.catch(() =&gt; {
console.log('cancelled');
});
};
&lt;/script&gt;

&lt;style scoped&gt;
.test-container {
padding: 20px;
}
&lt;/style&gt;
