<!--
  SEO管理页面
  用于管理网站的SEO相关功能
-->
<template>
  <div class="seo-management">
    <div class="page-header">
      <h1>SEO管理</h1>
      <p>管理网站的搜索引擎优化设置</p>
    </div>

    <div class="management-content">
      <!-- Sitemap管理 -->
      <el-card class="management-card">
        <template #header>
          <div class="card-header">
            <h2>Sitemap管理</h2>
            <div class="header-actions">
              <el-button @click="generateSitemap" :loading="sitemapLoading" type="primary">
                生成Sitemap
              </el-button>
              <el-button @click="downloadSitemapFile" :disabled="!sitemapContent">
                下载Sitemap
              </el-button>
            </div>
          </div>
        </template>

        <div class="sitemap-section">
          <div v-if="sitemapStats" class="sitemap-stats">
            <el-row :gutter="16">
              <el-col :span="6">
                <el-statistic title="总URL数" :value="sitemapStats.total" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="静态页面" :value="sitemapStats.static" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="内容页面" :value="sitemapStats.content" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="最后更新" :value="lastSitemapUpdate" format="YYYY/MM/DD HH:mm:ss" />
              </el-col>
            </el-row>
          </div>

          <div v-if="sitemapContent" class="sitemap-preview">
            <h3>Sitemap预览</h3>
            <el-input v-model="sitemapContent" type="textarea" :rows="10" readonly class="sitemap-textarea" />
          </div>
        </div>
      </el-card>

      <!-- Robots.txt管理 -->
      <el-card class="management-card">
        <template #header>
          <div class="card-header">
            <h2>Robots.txt管理</h2>
            <div class="header-actions">
              <el-button @click="generateRobots" :loading="robotsLoading" type="primary">
                生成Robots.txt
              </el-button>
              <el-button @click="downloadRobotsFile" :disabled="!robotsContent">
                下载Robots.txt
              </el-button>
            </div>
          </div>
        </template>

        <div class="robots-section">
          <div class="robots-options">
            <el-form :model="robotsOptions" label-width="120px">
              <el-form-item label="环境类型">
                <el-radio-group v-model="robotsOptions.environment">
                  <el-radio value="production">生产环境</el-radio>
                  <el-radio value="development">开发环境</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="爬取延迟">
                <el-input-number v-model="robotsOptions.crawlDelay" :min="0" :max="60" :step="1"
                  controls-position="right" />
                <span class="form-help">秒（0表示无延迟）</span>
              </el-form-item>
            </el-form>
          </div>

          <div v-if="robotsStats" class="robots-stats">
            <el-descriptions title="Robots.txt统计" :column="2" border>
              <el-descriptions-item label="总行数">{{ robotsStats.totalLines }}</el-descriptions-item>
              <el-descriptions-item label="User-agent数">{{ robotsStats.userAgents.length }}</el-descriptions-item>
              <el-descriptions-item label="Disallow规则">{{ robotsStats.disallowRules }}</el-descriptions-item>
              <el-descriptions-item label="Allow规则">{{ robotsStats.allowRules }}</el-descriptions-item>
              <el-descriptions-item label="Sitemap数">{{ robotsStats.sitemaps.length }}</el-descriptions-item>
              <el-descriptions-item label="爬取延迟">{{ robotsStats.crawlDelays }}</el-descriptions-item>
            </el-descriptions>
          </div>

          <div v-if="robotsContent" class="robots-preview">
            <h3>Robots.txt预览</h3>
            <el-input v-model="robotsContent" type="textarea" :rows="15" readonly class="robots-textarea" />
          </div>
        </div>
      </el-card>

      <!-- SEO检查 -->
      <el-card class="management-card">
        <template #header>
          <div class="card-header">
            <h2>SEO检查</h2>
            <div class="header-actions">
              <el-button @click="runSEOCheck" :loading="seoCheckLoading" type="success">
                运行检查
              </el-button>
            </div>
          </div>
        </template>

        <div class="seo-check-section">
          <div v-if="seoCheckResults" class="seo-results">
            <el-alert v-if="seoCheckResults.errors.length > 0" title="发现问题" type="error"
              :description="`发现 ${seoCheckResults.errors.length} 个错误`" show-icon class="result-alert" />

            <el-alert v-if="seoCheckResults.warnings.length > 0" title="需要注意" type="warning"
              :description="`发现 ${seoCheckResults.warnings.length} 个警告`" show-icon class="result-alert" />

            <el-alert v-if="seoCheckResults.errors.length === 0 && seoCheckResults.warnings.length === 0" title="检查通过"
              type="success" description="所有SEO检查项目都通过了" show-icon class="result-alert" />

            <!-- 详细结果 -->
            <div v-if="seoCheckResults.errors.length > 0" class="check-details">
              <h4>错误详情</h4>
              <ul class="issue-list error-list">
                <li v-for="error in seoCheckResults.errors" :key="error">{{ error }}</li>
              </ul>
            </div>

            <div v-if="seoCheckResults.warnings.length > 0" class="check-details">
              <h4>警告详情</h4>
              <ul class="issue-list warning-list">
                <li v-for="warning in seoCheckResults.warnings" :key="warning">{{ warning }}</li>
              </ul>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 页面SEO状态 -->
      <el-card class="management-card">
        <template #header>
          <div class="card-header">
            <h2>页面SEO状态</h2>
            <div class="header-actions">
              <el-button @click="refreshPageStatus" :loading="pageStatusLoading" type="info">
                刷新状态
              </el-button>
            </div>
          </div>
        </template>

        <div class="page-status-section">
          <el-table v-if="pageStatusList" :data="pageStatusList" style="width: 100%">
            <el-table-column prop="path" label="页面路径" min-width="200" />
            <el-table-column prop="title" label="页面标题" min-width="250" />
            <el-table-column prop="description" label="描述" min-width="300" show-overflow-tooltip />
            <el-table-column prop="hasStructuredData" label="结构化数据" width="120">
              <template #default="{ row }">
                <el-tag :type="row.hasStructuredData ? 'success' : 'danger'">
                  {{ row.hasStructuredData ? '已配置' : '未配置' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="hasOpenGraph" label="Open Graph" width="120">
              <template #default="{ row }">
                <el-tag :type="row.hasOpenGraph ? 'success' : 'danger'">
                  {{ row.hasOpenGraph ? '已配置' : '未配置' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { generateSitemapFromAPI, validateSitemap, getSitemapStats, downloadSitemap } from '@/utils/sitemapGenerator';
import { generateProdRobotsTxt, generateDevRobotsTxt, validateRobotsTxt, getRobotsTxtStats, downloadRobotsTxt } from '@/utils/robotsGenerator';
import { useSEO, PAGE_SEO_CONFIG } from '@/composables/useSEO';

// 初始化SEO
useSEO({
  title: 'SEO管理 - Echo Lab 管理后台',
  description: '管理Echo Lab网站的搜索引擎优化设置，包括sitemap、robots.txt等',
  keywords: 'SEO管理,sitemap,robots.txt,搜索引擎优化'
});

// Sitemap相关状态
const sitemapLoading = ref(false);
const sitemapContent = ref('');
const sitemapStats = ref(null);
const lastSitemapUpdate = ref(new Date());

// Robots.txt相关状态
const robotsLoading = ref(false);
const robotsContent = ref('');
const robotsStats = ref(null);
const robotsOptions = reactive({
  environment: 'production',
  crawlDelay: 1
});

// SEO检查相关状态
const seoCheckLoading = ref(false);
const seoCheckResults = ref(null);

// 页面状态相关
const pageStatusLoading = ref(false);
const pageStatusList = ref(null);

// 生成Sitemap
async function generateSitemap() {
  sitemapLoading.value = true;
  try {
    const sitemap = await generateSitemapFromAPI();
    sitemapContent.value = sitemap;
    sitemapStats.value = getSitemapStats(sitemap);
    lastSitemapUpdate.value = new Date();

    // 验证sitemap
    const isValid = validateSitemap(sitemap);
    if (isValid) {
      ElMessage.success('Sitemap生成成功');
    } else {
      ElMessage.warning('Sitemap生成完成，但格式可能有问题');
    }
  } catch (error) {
    console.error('生成Sitemap失败:', error);
    ElMessage.error('生成Sitemap失败');
  } finally {
    sitemapLoading.value = false;
  }
}

// 下载Sitemap文件
function downloadSitemapFile() {
  if (!sitemapContent.value) {
    ElMessage.warning('请先生成Sitemap');
    return;
  }
  downloadSitemap();
  ElMessage.success('Sitemap下载已开始');
}

// 生成Robots.txt
function generateRobots() {
  robotsLoading.value = true;
  try {
    const robots = robotsOptions.environment === 'production'
      ? generateProdRobotsTxt()
      : generateDevRobotsTxt();

    robotsContent.value = robots;
    robotsStats.value = getRobotsTxtStats(robots);

    // 验证robots.txt
    const validation = validateRobotsTxt(robots);
    if (validation.isValid) {
      ElMessage.success('Robots.txt生成成功');
    } else {
      ElMessage.warning(`Robots.txt生成完成，但有 ${validation.errors.length} 个错误`);
    }
  } catch (error) {
    console.error('生成Robots.txt失败:', error);
    ElMessage.error('生成Robots.txt失败');
  } finally {
    robotsLoading.value = false;
  }
}

// 下载Robots.txt文件
function downloadRobotsFile() {
  if (!robotsContent.value) {
    ElMessage.warning('请先生成Robots.txt');
    return;
  }
  downloadRobotsTxt(robotsOptions.environment === 'production');
  ElMessage.success('Robots.txt下载已开始');
}

// 运行SEO检查
async function runSEOCheck() {
  seoCheckLoading.value = true;
  try {
    const errors = [];
    const warnings = [];

    // 检查sitemap
    if (!sitemapContent.value) {
      warnings.push('尚未生成Sitemap');
    } else {
      const sitemapValid = validateSitemap(sitemapContent.value);
      if (!sitemapValid) {
        errors.push('Sitemap格式无效');
      }
    }

    // 检查robots.txt
    if (!robotsContent.value) {
      warnings.push('尚未生成Robots.txt');
    } else {
      const robotsValidation = validateRobotsTxt(robotsContent.value);
      if (!robotsValidation.isValid) {
        errors.push(...robotsValidation.errors);
      }
      warnings.push(...robotsValidation.warnings);
    }

    // 检查页面SEO配置
    const pageConfigErrors = checkPageSEOConfig();
    errors.push(...pageConfigErrors);

    seoCheckResults.value = { errors, warnings };
    ElMessage.success('SEO检查完成');
  } catch (error) {
    console.error('SEO检查失败:', error);
    ElMessage.error('SEO检查失败');
  } finally {
    seoCheckLoading.value = false;
  }
}

// 检查页面SEO配置
function checkPageSEOConfig() {
  const errors = [];

  // 检查PAGE_SEO_CONFIG中的配置
  Object.entries(PAGE_SEO_CONFIG).forEach(([page, config]) => {
    if (!config.title) {
      errors.push(`页面 ${page} 缺少标题配置`);
    }
    if (!config.description) {
      errors.push(`页面 ${page} 缺少描述配置`);
    }
    if (!config.keywords) {
      errors.push(`页面 ${page} 缺少关键词配置`);
    }
  });

  return errors;
}

// 刷新页面状态
function refreshPageStatus() {
  pageStatusLoading.value = true;
  try {
    // 模拟页面状态数据
    pageStatusList.value = Object.entries(PAGE_SEO_CONFIG).map(([page, config]) => ({
      path: page === 'home' ? '/' : `/${page}`,
      title: config.title,
      description: config.description,
      hasStructuredData: true, // 这里应该实际检查
      hasOpenGraph: true // 这里应该实际检查
    }));

    ElMessage.success('页面状态刷新完成');
  } catch (error) {
    console.error('刷新页面状态失败:', error);
    ElMessage.error('刷新页面状态失败');
  } finally {
    pageStatusLoading.value = false;
  }
}

// 组件挂载时初始化
onMounted(() => {
  generateSitemap();
  generateRobots();
  refreshPageStatus();
});
</script>

<style scoped>
.seo-management {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 1.75rem;
  color: #303133;
  margin-bottom: 0.5rem;
}

.page-header p {
  color: #606266;
  font-size: 1rem;
}

.management-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.management-card {
  border-radius: 0.5rem;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  font-size: 1.25rem;
  color: #303133;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.sitemap-stats {
  margin-bottom: 1.5rem;
}

.sitemap-preview,
.robots-preview {
  margin-top: 1.5rem;
}

.sitemap-preview h3,
.robots-preview h3 {
  font-size: 1rem;
  color: #303133;
  margin-bottom: 0.75rem;
}

.sitemap-textarea,
.robots-textarea {
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

.robots-options {
  margin-bottom: 1.5rem;
}

.form-help {
  margin-left: 0.5rem;
  color: #909399;
  font-size: 0.875rem;
}

.robots-stats {
  margin-bottom: 1.5rem;
}

.seo-results {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.result-alert {
  margin-bottom: 1rem;
}

.check-details {
  margin-top: 1rem;
}

.check-details h4 {
  font-size: 1rem;
  color: #303133;
  margin-bottom: 0.5rem;
}

.issue-list {
  margin: 0;
  padding-left: 1.5rem;
}

.issue-list li {
  margin-bottom: 0.25rem;
}

.error-list li {
  color: #f56c6c;
}

.warning-list li {
  color: #e6a23c;
}

.page-status-section {
  margin-top: 1rem;
}

/* 移动端样式通过条件类名处理 */
</style>
