<!--
  内容信息页面
  提供关于视频内容的详细信息
-->
<template>
  <div class="content-info-page">
    <!-- 页面标题栏 -->
    <SmartPageHeader title="关于 Echo Lab 视频内容" :force-show-back="true" />

    <div class="page-content">
      <div class="info-card">
        <h2>精听练习视频平台</h2>
        <p>Echo Lab 是一个精听练习视频播放平台，提供灵活的播放控制功能，帮助语言学习者提升听力理解能力。我们希望通过以下说明帮助您了解我们的内容特点和播放功能。</p>

        <h3>播放功能特点</h3>
        <ul>
          <li><strong>自定义播放环节</strong>：可以选择启用或禁用不同的播放环节，如通读、重复等</li>
          <li><strong>调整重复次数</strong>：根据学习需求自定义每个句子的重复播放次数</li>
          <li><strong>灵活控制播放速度</strong>：提供多种播放速度选项，从0.9倍速到1.1倍速</li>
          <li><strong>精确导航</strong>：支持键盘快捷键，方便在句子间跳转和重复播放</li>
          <li><strong>播放列表</strong>：显示完整内容大纲，点击即可跳转到指定位置</li>
          <li><strong>个性化设置</strong>：可调整字体大小、颜色和背景，提供舒适的观看体验</li>
        </ul>
      </div>

      <div class="info-card">
        <h2>内容制作方式</h2>

        <h3>文本内容</h3>
        <ul>
          <li>部分文本内容使用 AI 辅助生成，经过人工审核</li>
          <li>可能存在语法或表达不够自然的情况</li>
          <li>内容主题和难度水平各异，适合不同学习阶段的用户</li>
        </ul>

        <h3>音频合成</h3>
        <ul>
          <li>使用 TTS（文本转语音）技术合成音频</li>
          <li>在音调、语速、停顿和情感表达上可能与真人发音有差距</li>
          <li>不同语言和声音的合成质量可能有所不同</li>
          <li>合成音频可能缺乏自然的语调变化和情感表达</li>
        </ul>
      </div>

      <div class="info-card">
        <h2>适用人群</h2>
        <p>Echo Lab 视频内容适合：</p>
        <ul>
          <li>对合成音频质量要求不高的语言学习者</li>
          <li>需要大量重复练习听力的学习者</li>
          <li>希望通过多种方式提升语言能力的学习者</li>
          <li>将其作为辅助学习工具，而非主要学习资源的用户</li>
        </ul>

        <h3>使用建议</h3>
        <ul>
          <li>将 Echo Lab 视频作为语言学习的补充材料</li>
          <li>结合专业的语言学习资源和真人发音材料一起使用</li>
          <li>利用重复播放和精听功能加强听力理解能力</li>
          <li>关注内容本身，而非过度关注发音的细微差别</li>
        </ul>

        <div class="note-box">
          <p>我们持续改进内容质量和音频合成技术，希望为您提供更好的学习体验。如有任何建议或反馈，欢迎与我们联系。</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import SmartPageHeader from '@/components/common/SmartPageHeader.vue';
import { useSEO, PAGE_SEO_CONFIG } from '@/composables/useSEO';

// 初始化SEO
useSEO(PAGE_SEO_CONFIG.contentInfo);
</script>

<style scoped>
.content-info-page {
  min-height: 100%;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
}



.page-content {
  flex: 1;
  max-width: 50rem;
  margin: 0 auto;
  padding: 2rem 1.5rem;
  width: 100%;
  box-sizing: border-box;
}

.info-card {
  background-color: #ffffff;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.info-card h2 {
  font-size: 1.25rem;
  margin: 0 0 1rem 0;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 0.5rem;
}

.info-card h3 {
  font-size: 1.125rem;
  margin: 1.5rem 0 0.5rem 0;
  color: #409EFF;
}

.info-card p {
  margin: 0.5rem 0;
  line-height: 1.6;
}

.info-card ul {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.info-card li {
  margin-bottom: 0.5rem;
  line-height: 1.6;
  word-break: break-word;
}

.note-box {
  margin-top: 1.5rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
  font-style: italic;
}

.note-box p {
  margin: 0;
  color: #606266;
}

/* 响应式布局 - 使用类名而非媒体查询 */
.tablet-device .page-header {
  padding: 0.75rem 0;
}

.tablet-device .header-content {
  padding: 0 1rem;
}

.tablet-device .page-header h1 {
  font-size: 1.25rem;
}

.tablet-device .page-content {
  padding: 1.5rem 1rem;
}

.tablet-device .info-card {
  padding: 1.25rem;
  margin-bottom: 1rem;
}

.tablet-device .info-card h2 {
  font-size: 1.125rem;
}

.tablet-device .info-card h3 {
  font-size: 1rem;
}

.tablet-device .info-card ul {
  padding-left: 1.25rem;
}

.mobile-device .page-header h1 {
  font-size: 1.125rem;
}

.mobile-device .page-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100% - 4rem);
}

.mobile-device .page-content {
  padding: 1rem;
}

.mobile-device .info-card {
  padding: 1rem;
}

.mobile-device .info-card h2 {
  font-size: 1.0625rem;
}

.mobile-device .info-card h3 {
  font-size: 0.9375rem;
}

.mobile-device .info-card p,
.mobile-device .info-card li {
  font-size: 0.875rem;
}
</style>
