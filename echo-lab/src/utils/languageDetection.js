// languageDetection.js

/**
 * 检测中文变体（简体/繁体）
 * 使用常用繁体字特征检测
 */
function detectChineseVariant(text) {
  // 繁体中文特有字符（从你提供的文件中提取）- 第一部分
  const traditionalChars1 =
    /[繁體語國門時東風電開關燈車馬書學習會議問題發現實際實現實驗來個們對說過還沒這樣應該經過後時間現在開始結束進行處理發生變化機會條件環境情況狀態內容資料資訊資源資金資產資格資質資助資深資歷資本網絡負債認證評估計劃專家豐富市場分析系統配置投入管理審查標準項目顧問證明運作技術整合運用要求認定政策經理審核統計利用流向負債表評定]/g;

  // 第二部分 - 更多繁体字
  const traditionalChars2 =
    /[業務營運營業營養營造營銷營收營利營建營運營業額營業稅營業執照營業時間營業收入營業成本營業利潤營業費用營業外收入營業外支出營業稅金營業收益營業虧損營業週期營業日營業額度營業範圍營業性質營業登記營業許可營業場所營業活動營業狀況營業績效營業目標營業計劃營業策略營業模式營業流程營業管理營業發展營業創新營業轉型營業升級營業擴張營業收購營業合併營業分割營業重組營業改制營業上市營業下市營業停牌營業復牌營業除權營業除息營業配股營業增資營業減資營業清算營業破產營業重整營業和解營業仲裁營業訴訟營業糾紛營業爭議營業協商營業談判營業簽約營業履約營業違約營業賠償營業責任營業義務營業權利營業利益營業損失營業風險營業機遇營業挑戰營業競爭營業合作營業聯盟營業併購營業投資營業融資營業貸款營業擔保營業抵押營業質押營業保險營業稅務營業會計營業審計營業監督營業檢查營業評估營業諮詢營業顧問營業培訓營業教育營業研究營業開發營業設計營業製造營業生產營業採購營業銷售營業服務營業維修營業保養營業更新營業升級營業改進營業優化營業創新營業發明營業專利營業商標營業版權營業著作權營業知識產權營業技術轉移營業技術合作營業技術交流營業技術培訓營業技術服務營業技術支持營業技術維護營業技術更新營業技術升級營業技術改進營業技術優化營業技術創新營業技術發明營業技術專利營業技術商標營業技術版權營業技術著作權營業技術知識產權]/g;

  // 第三部分 - 从你文件中提取的更多繁体字
  const traditionalChars3 =
    /[並丟兩嚴臨麗舉義烏樂書亂買雲亙產親亮從侖倉們價傳傷倫偽傾僅億儀儂優償儲兒兌黨兜內冊寫軍農況凍減幾鳳處鳧鳩鳴鳳凰鳳梨鳳仙花鳳眼蓮鳳尾竹鳳尾魚鳳毛麟角鳳冠霞帔鳳簫龍管鳳翥龍翔鳳毛濟美鳳鳴朝陽鳳凰于飛鳳凰來儀鳳凰涅槃劉則剛創刪別刧判利刪別刬刮到制刷券刹刺刻刽刿剎剏剛剝剩剪副剰割創剷剸剹剺剻剼剽剾剿劃劄劇劈劉劊劌劍劎劏劐劑劒劓劔劕劖劗劘劙劚勁勃勅勇勉勛勝勞募勢勤勦勧勩勪勫勬勭勮勯勰勱勲勳勴勵勶勷勸匯匱區醫匼匽匾匿區協單賣南博卜占卡盧卣鹵卦臥衛卻卷卸卹卻卽卿廠厤厭厲厳厴厵厶厷厸厹厺去厼厽厾縣叄參叅叆叇發叒叔取受變敘叛叢叢]/g;

  // 第四部分 - 更多常用繁体字
  const traditionalChars4 =
    /[員哢哣哤哥哦哧哨哩哪哫哬哭哮哯哰哱哲哳哴哵哶哷哸哹哺哻哼哽哾哿唀唁唂唃唄唅唆唇唈唉唊唋唌唍唎唏唐唑唒唓唔唕唖唗唘唙唚唛唜唝唞唟唠唡唢唣唤唥唦唧唨唩唪唫唬唭售唯唰唱唲唳唴唵唶唷唸唹唺唻唼唽唾唿啀啁啂啃啄啅商啇啈啉啊啋啌啍啎問啐啑啒啓啔啕啖啗啘啙啚啛啜啝啞啟啠啡啢啣啤啥啦啧啨啩啪啫啬啭啮啯啰啱啲啳啴啵啶啷啸啹啺啻啼啽啾啿喀喁喂喃善喅喆喇喈喉喊喋喌喍喎喏喐喑喒喓喔喕喖喗喘喙喚喛喜喝喞喟喠喡喢喣喤喥喦喧喨喩喪喫喬喭單喯喰喱喲喳喴喵営喷喸喹喺喻喼喽喾喿嗀嗁嗂嗃嗄嗅嗆嗇嗈嗉嗊嗋嗌嗍嗎嗏嗐嗑嗒嗓嗔嗕嗖嗗嗘嗙嗚嗛嗜嗝嗞嗟嗠嗡嗢嗣嗤嗥嗦嗧嗨嗩嗪嗫嗬嗭嗮嗯嗰嗱嗲嗳嗴嗵嗶嗷嗸嗹嗺嗻嗼嗽嗾嗿嘀嘁嘂嘃嘄嘅嘆嘇嘈嘉嘊嘋嘌嘍嘎嘏嘐嘑嘒嘓嘔嘕嘖嘗嘘嘙嘚嘛嘜嘝嘞嘟嘠嘡嘢嘣嘤嘥嘦嘧嘨嘩嘪嘫嘬嘭嘮嘯嘰嘱嘲嘳嘴嘵嘶嘷嘸嘹嘺嘻嘼嘽嘾嘿噀噁噂噃噄噅噆噇噈噉噊噋噌噍噎噏噐噑噒噓噔噕噖噗噘噙噚噛噜噝噞噟噠噡噢噣噤噥噦噧器噩噪噫噬噭噮噯噰噱噲噳噴噵噶噷噸噹噺噻噼噽噾噿嚀嚁嚂嚃嚄嚅嚆嚇嚈嚉嚊嚋嚌嚍嚎嚏嚐嚑嚒嚓嚔嚕嚖嚗嚘嚙嚚嚛嚜嚝嚞嚟嚠嚡嚢嚣嚤嚥嚦嚧嚨嚩嚪嚫嚬嚭嚮嚯嚰嚱嚲嚳嚴嚵嚶嚷嚸嚹嚺嚻嚼嚽嚾嚿囀囁囂囃囄囅囆囇囈囉囊囋囌囍囎囏囐囑囒囓囔囕囖囗囘囙囚四囜囝回囟因囡团団囤囥囦囧囨囩囪囫囬园囮囯困囱囲図围囵囶囷囸囹固囻囼国图囿圀圁圂圃圄圅圆圇圈圉圊國圌圍圎圏圐圑園圓圔圕圖圗團圙圚圛圜圝圞]/g;

  // 第五部分 - 继续添加繁体字
  const traditionalChars5 =
    /[壓壔壕壖壗壘壙壚壛壜壝壞壟壠壡壢壣壤壥壦壧壨壩壪壬壭壮壯聲壱賣殼壴壵壶壷壸壹壺壻壼壽壾壿夀夁夂夃處夅夆備夈變夊夋夌復夎夏夐夑夒夓夔夕外夗夘夙多夛夜夝夞夠夡夢夣夤夥夦大夨天太夫夬夭央夯夰失夲夳頭夵夶夷夸夾夺夻夼夽夾夿奀奁奂奃奄奅奆奇奈奉奊奮奌奍奎奏奐契奒奓奔奕獎套奘奙奚奛奜奝奞奟奠奡奢奣奤奧奦奧奨奩奪奫奬奭奮奯奰奱奲女奴奵奶奷奸她奺奻奼好奾奿妀妁如妃妄妅妝婦媽妉妊妋妌妍妎妏妐妑妒妓妔妕妖妗妘妙妚妛妜妝妞妟妠妡妢妣妤妥妦妧妨妩妪妫妬妭妮妯妰妱妲妳妴妵妶妷妸妹妺妻妼妽妾妿姀姁姂姃姄姅姆姇姈姉姊始姌姍姎姏姐姑姒姓委姕姖姗姘姙姚姛姜姝姞姟姠姡姢姣姤姥姦姧姨姩姪姫姬姭姮姯姰姱姲姳姴姵姶姷姸姹姺姻姼姽姾姿娀威娂娃娄娅娆嬌娈娉娊娋娌娍娎娏娐娑娓娔娕娖娗娘娙娚娛娜娝娞娟娠娡娢娣娤娥娦娧娨娩娪娫娬娭娮娯娰娛娲娳嫻娵娶娷娸娹娺娻娼娽娾娿婀婁婂婃婄婅婆婇婈婉婊婋婌婍婎婏婐婑婒婓婔婕婖婗婘婙婚婛婜婝婞婟婠婡婢婣婤婥婦婧婨婩婪婫婬婭婮婯婰婱婲婳嬰嬋嬌嬸嬹嬺嬻嬼嬽嬾嬿孀孁孂孃孄孅孆孇孈孉孊孋孌孍孎孏子孑孒孓孔孕孖字存孫孚孛孜孝孞孟孠孡孢季孤孥學孧孨孩孿孫孬孭孮孯孰孱孲孳孴孵孶孷學孹孺孻孼孽孾孿]/g;

  // 只要有一个正则匹配到繁体字，就判断为繁体中文
  if (
    traditionalChars1.test(text) ||
    traditionalChars2.test(text) ||
    traditionalChars3.test(text) ||
    traditionalChars4.test(text) ||
    traditionalChars5.test(text)
  ) {
    return "zh-TW";
  }

  return "zh-CN";
}

/**
 * 检测文本语言
 * @param {string} text - 要检测的文本
 * @returns {string} 检测到的语言代码 (en, ja, zh-CN, zh-TW, auto, unknown)
 */
export function detectLanguage(text) {
  if (!text || typeof text !== "string" || text.trim() === "") return "auto";

  // 日语检测（假名字符）
  if (/[\u3040-\u30FF\u31F0-\u31FF\uFF65-\uFF9F]/.test(text)) {
    return "ja";
  }

  // 中文检测（汉字字符）
  if (/[\u4e00-\u9fff]/.test(text)) {
    return detectChineseVariant(text);
  }

  // 英文检测（纯ASCII字符）
  if (/^[\u0000-\u007F\s\.,'"\-!?]+$/.test(text)) {
    return "en";
  }

  return "unknown";
}
