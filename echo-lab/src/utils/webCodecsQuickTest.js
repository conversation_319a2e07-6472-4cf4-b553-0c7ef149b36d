/**
 * WebCodecs快速测试工具
 * 用于验证基本功能是否正常
 */

import { webCodecsExporter } from "./webCodecsVideoExporter.js";

/**
 * 创建测试用的时间线数据
 */
function createTestTimeline() {
  return [
    {
      id: "test-1",
      type: "text",
      content: {
        text: "Hello WebCodecs!",
        translation: "WebCodecs测试",
      },
      duration: 2000,
      startTime: 0,
    },
    {
      id: "test-2",
      type: "text",
      content: {
        text: "This is a test video",
        translation: "这是一个测试视频",
      },
      duration: 3000,
      startTime: 2000,
    },
  ];
}

/**
 * 创建测试用的音频缓冲区
 */
function createTestAudioBuffer() {
  // 使用24kHz采样率，与项目保持一致
  const sampleRate = 24000;
  const audioContext = new (window.AudioContext || window.webkitAudioContext)({
    sampleRate: sampleRate,
  });
  const duration = 5; // 5秒
  const frameCount = sampleRate * duration;

  const audioBuffer = audioContext.createBuffer(1, frameCount, sampleRate);
  const channelData = audioBuffer.getChannelData(0);

  // 生成简单的正弦波测试音频
  for (let i = 0; i < frameCount; i++) {
    channelData[i] = Math.sin((2 * Math.PI * 440 * i) / sampleRate) * 0.1; // 440Hz正弦波
  }

  return audioBuffer;
}

/**
 * 创建测试用的渲染函数
 */
function createTestRenderFunction() {
  return async (item) => {
    // 创建一个简单的测试容器
    const container = document.createElement("div");
    container.style.width = "640px";
    container.style.height = "480px";
    container.style.backgroundColor = "#000000";
    container.style.display = "flex";
    container.style.flexDirection = "column";
    container.style.justifyContent = "center";
    container.style.alignItems = "center";
    container.style.color = "#FFFFFF";
    container.style.fontFamily = "Arial, sans-serif";
    container.style.fontSize = "48px";
    container.style.textAlign = "center";
    container.style.padding = "20px";
    container.style.boxSizing = "border-box";

    // 添加主要文本
    const mainText = document.createElement("div");
    mainText.textContent = item.content.text;
    mainText.style.marginBottom = "20px";
    container.appendChild(mainText);

    // 添加翻译文本
    if (item.content.translation) {
      const translationText = document.createElement("div");
      translationText.textContent = item.content.translation;
      translationText.style.fontSize = "32px";
      translationText.style.color = "#CCCCCC";
      container.appendChild(translationText);
    }

    return container;
  };
}

/**
 * 运行WebCodecs快速测试
 */
export async function runWebCodecsQuickTest() {
  console.log("开始WebCodecs快速测试...");

  try {
    // 检查支持
    if (!webCodecsExporter.isSupported) {
      throw new Error("当前浏览器不支持WebCodecs API");
    }

    console.log("✅ WebCodecs API支持检测通过");

    // 创建测试数据
    const timeline = createTestTimeline();
    const audioBuffer = createTestAudioBuffer();
    const renderContent = createTestRenderFunction();

    console.log("✅ 测试数据创建完成");

    // 执行导出测试 - 使用低质量进行快速测试
    const result = await webCodecsExporter.exportVideo(
      timeline,
      audioBuffer,
      renderContent,
      {
        width: 640,
        height: 480,
        frameRate: 30,
        quality: "low",
        fileName: "webcodecs_test",
        generateSubtitles: false,
        onProgress: (progress) => {
          console.log(
            `进度: ${progress.phase} - ${progress.progress.toFixed(1)}%`
          );
        },
      }
    );

    console.log("✅ 视频导出成功!");
    console.log(
      "视频大小:",
      (result.videoBlob.size / 1024 / 1024).toFixed(2),
      "MB"
    );

    // 自动下载测试视频
    webCodecsExporter.downloadVideo(result.videoBlob, result.fileName);

    return {
      success: true,
      videoSize: result.videoBlob.size,
      fileName: result.fileName,
    };
  } catch (error) {
    console.error("❌ WebCodecs测试失败:", error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * 运行基础兼容性检查
 */
export function runBasicCompatibilityCheck() {
  const results = {
    VideoEncoder: "VideoEncoder" in window,
    VideoFrame: "VideoFrame" in window,
    AudioEncoder: "AudioEncoder" in window,
    AudioData: "AudioData" in window,
    mp4Muxer: false,
  };

  // 检查mp4-muxer导入和基本功能
  try {
    import("mp4-muxer")
      .then(({ Muxer, ArrayBufferTarget }) => {
        try {
          // 测试创建Muxer实例
          const target = new ArrayBufferTarget();
          const testMuxer = new Muxer({
            target,
            video: {
              codec: "avc",
              width: 640,
              height: 480,
              frameRate: 30,
            },
            audio: {
              codec: "aac",
              numberOfChannels: 1,
              sampleRate: 44100,
            },
            fastStart: "in-memory",
            firstTimestampBehavior: "offset",
          });

          results.mp4Muxer = true;
          console.log("✅ mp4-muxer库可以正常导入和创建实例");

          // 清理测试实例
          testMuxer.finalize();
        } catch (error) {
          console.error("❌ mp4-muxer实例创建失败:", error);
          results.mp4Muxer = false;
        }
      })
      .catch((error) => {
        console.error("❌ mp4-muxer库导入失败:", error);
        results.mp4Muxer = false;
      });
  } catch (error) {
    console.error("❌ mp4-muxer库检查失败:", error);
    results.mp4Muxer = false;
  }

  const overall =
    results.VideoEncoder &&
    results.VideoFrame &&
    results.AudioEncoder &&
    results.AudioData;

  console.log("基础兼容性检查结果:");
  console.log("- VideoEncoder:", results.VideoEncoder ? "✅" : "❌");
  console.log("- VideoFrame:", results.VideoFrame ? "✅" : "❌");
  console.log("- AudioEncoder:", results.AudioEncoder ? "✅" : "❌");
  console.log("- AudioData:", results.AudioData ? "✅" : "❌");
  console.log("- 总体支持:", overall ? "✅" : "❌");

  return {
    ...results,
    overall,
  };
}

// 在开发环境下添加到全局
if (import.meta.env.DEV) {
  window.webCodecsQuickTest = {
    runQuickTest: runWebCodecsQuickTest,
    runCompatibilityCheck: runBasicCompatibilityCheck,
  };

  console.log("WebCodecs快速测试工具已加载:");
  console.log("- 运行兼容性检查: webCodecsQuickTest.runCompatibilityCheck()");
  console.log("- 运行完整测试: webCodecsQuickTest.runQuickTest()");
}
