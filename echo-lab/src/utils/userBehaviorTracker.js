/**
 * 用户行为追踪器
 * 收集用户交互数据用于错误分析
 */

class UserBehaviorTracker {
  constructor() {
    this.isInitialized = false;
    this.maxHistoryLength = 10;
    this.debounceTime = 100;
    this.lastScrollTime = 0;
  }

  /**
   * 初始化行为追踪
   */
  initialize() {
    if (this.isInitialized) {
      return;
    }

    this.initializeCounters();
    this.setupEventListeners();
    this.trackPageNavigation();
    this.isInitialized = true;

    console.log('用户行为追踪器已初始化');
  }

  /**
   * 初始化计数器
   */
  initializeCounters() {
    if (!sessionStorage.getItem('user_clicks')) {
      sessionStorage.setItem('user_clicks', '0');
    }
    if (!sessionStorage.getItem('user_scrolls')) {
      sessionStorage.setItem('user_scrolls', '0');
    }
    if (!sessionStorage.getItem('user_keypresses')) {
      sessionStorage.setItem('user_keypresses', '0');
    }
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 点击事件追踪
    document.addEventListener('click', (event) => {
      this.trackClick(event);
    }, { passive: true });

    // 滚动事件追踪（防抖）
    document.addEventListener('scroll', () => {
      this.trackScroll();
    }, { passive: true });

    // 键盘事件追踪
    document.addEventListener('keydown', (event) => {
      this.trackKeyPress(event);
    }, { passive: true });

    // 页面焦点追踪
    window.addEventListener('focus', () => {
      this.trackFocus();
    });

    window.addEventListener('blur', () => {
      this.trackBlur();
    });

    // 页面可见性变化
    document.addEventListener('visibilitychange', () => {
      this.trackVisibilityChange();
    });
  }

  /**
   * 追踪点击事件
   * @param {Event} event - 点击事件
   */
  trackClick(event) {
    try {
      // 增加点击计数
      const clicks = parseInt(sessionStorage.getItem('user_clicks') || '0') + 1;
      sessionStorage.setItem('user_clicks', clicks.toString());

      // 记录点击详情
      const clickData = {
        timestamp: Date.now(),
        target: this.getElementInfo(event.target),
        coordinates: {
          x: event.clientX,
          y: event.clientY,
          pageX: event.pageX,
          pageY: event.pageY
        }
      };

      this.addToHistory('clicks', clickData);
    } catch (err) {
      console.warn('追踪点击事件失败:', err);
    }
  }

  /**
   * 追踪滚动事件
   */
  trackScroll() {
    try {
      const now = Date.now();
      if (now - this.lastScrollTime < this.debounceTime) {
        return;
      }
      this.lastScrollTime = now;

      // 增加滚动计数
      const scrolls = parseInt(sessionStorage.getItem('user_scrolls') || '0') + 1;
      sessionStorage.setItem('user_scrolls', scrolls.toString());

      // 记录滚动详情
      const scrollData = {
        timestamp: now,
        scrollX: window.scrollX || window.pageXOffset,
        scrollY: window.scrollY || window.pageYOffset,
        scrollHeight: document.documentElement.scrollHeight,
        clientHeight: document.documentElement.clientHeight
      };

      this.addToHistory('scrolls', scrollData);
    } catch (err) {
      console.warn('追踪滚动事件失败:', err);
    }
  }

  /**
   * 追踪键盘事件
   * @param {Event} event - 键盘事件
   */
  trackKeyPress(event) {
    try {
      // 增加按键计数
      const keypresses = parseInt(sessionStorage.getItem('user_keypresses') || '0') + 1;
      sessionStorage.setItem('user_keypresses', keypresses.toString());

      // 记录按键详情（不记录敏感信息）
      const keyData = {
        timestamp: Date.now(),
        key: event.key.length === 1 ? '[char]' : event.key, // 字符键统一标记
        code: event.code,
        ctrlKey: event.ctrlKey,
        altKey: event.altKey,
        shiftKey: event.shiftKey,
        metaKey: event.metaKey
      };

      this.addToHistory('keypresses', keyData);
    } catch (err) {
      console.warn('追踪键盘事件失败:', err);
    }
  }

  /**
   * 追踪页面焦点
   */
  trackFocus() {
    try {
      sessionStorage.setItem('last_focus_time', Date.now().toString());
    } catch (err) {
      console.warn('追踪页面焦点失败:', err);
    }
  }

  /**
   * 追踪页面失焦
   */
  trackBlur() {
    try {
      sessionStorage.setItem('last_blur_time', Date.now().toString());
    } catch (err) {
      console.warn('追踪页面失焦失败:', err);
    }
  }

  /**
   * 追踪页面可见性变化
   */
  trackVisibilityChange() {
    try {
      const visibilityData = {
        timestamp: Date.now(),
        hidden: document.hidden,
        visibilityState: document.visibilityState
      };

      this.addToHistory('visibility_changes', visibilityData);
    } catch (err) {
      console.warn('追踪页面可见性变化失败:', err);
    }
  }

  /**
   * 追踪页面导航
   */
  trackPageNavigation() {
    try {
      const currentPage = {
        url: window.location.href,
        pathname: window.location.pathname,
        timestamp: Date.now(),
        referrer: document.referrer
      };

      this.addToHistory('navigation_history', currentPage);
    } catch (err) {
      console.warn('追踪页面导航失败:', err);
    }
  }

  /**
   * 获取元素信息
   * @param {Element} element - DOM元素
   * @returns {Object} 元素信息
   */
  getElementInfo(element) {
    try {
      return {
        tagName: element.tagName?.toLowerCase(),
        id: element.id || null,
        className: element.className || null,
        textContent: element.textContent?.substring(0, 50) || null, // 限制长度
        type: element.type || null,
        name: element.name || null
      };
    } catch (err) {
      return { error: err.message };
    }
  }

  /**
   * 添加到历史记录
   * @param {string} key - 存储键
   * @param {Object} data - 数据
   */
  addToHistory(key, data) {
    try {
      const historyKey = `${key}_history`;
      let history = [];

      const existingHistory = sessionStorage.getItem(historyKey);
      if (existingHistory) {
        history = JSON.parse(existingHistory);
      }

      history.push(data);

      // 限制历史记录长度
      if (history.length > this.maxHistoryLength) {
        history = history.slice(-this.maxHistoryLength);
      }

      sessionStorage.setItem(historyKey, JSON.stringify(history));
    } catch (err) {
      console.warn(`添加${key}历史记录失败:`, err);
    }
  }

  /**
   * 获取用户行为摘要
   * @returns {Object} 行为摘要
   */
  getBehaviorSummary() {
    try {
      return {
        interactions: {
          clicks: parseInt(sessionStorage.getItem('user_clicks') || '0'),
          scrolls: parseInt(sessionStorage.getItem('user_scrolls') || '0'),
          keypresses: parseInt(sessionStorage.getItem('user_keypresses') || '0')
        },
        focus: {
          last_focus_time: sessionStorage.getItem('last_focus_time'),
          last_blur_time: sessionStorage.getItem('last_blur_time')
        },
        recent_clicks: this.getRecentHistory('clicks_history', 3),
        recent_navigation: this.getRecentHistory('navigation_history', 5)
      };
    } catch (err) {
      return { error: err.message };
    }
  }

  /**
   * 获取最近的历史记录
   * @param {string} key - 存储键
   * @param {number} count - 获取数量
   * @returns {Array} 历史记录
   */
  getRecentHistory(key, count = 5) {
    try {
      const history = sessionStorage.getItem(key);
      if (!history) return [];

      const parsed = JSON.parse(history);
      return parsed.slice(-count);
    } catch (err) {
      return [];
    }
  }

  /**
   * 清理历史数据
   */
  cleanup() {
    try {
      const keys = [
        'user_clicks', 'user_scrolls', 'user_keypresses',
        'clicks_history', 'scrolls_history', 'keypresses_history',
        'navigation_history', 'visibility_changes_history',
        'last_focus_time', 'last_blur_time'
      ];

      keys.forEach(key => {
        sessionStorage.removeItem(key);
      });

      console.log('用户行为数据已清理');
    } catch (err) {
      console.warn('清理用户行为数据失败:', err);
    }
  }
}

// 创建全局实例
export const userBehaviorTracker = new UserBehaviorTracker();

export default userBehaviorTracker;
