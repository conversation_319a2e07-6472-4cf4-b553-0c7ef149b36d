/**
 * 认证服务
 * 处理用户认证相关的API调用
 */
import httpClient from "@/utils/httpClient";
import { API_ENDPOINTS } from "@/config/api";

// 认证API端点
const AUTH_API = "/api/auth";

// 本地存储键
const TOKEN_KEY = "auth_token";
const USER_KEY = "auth_user";

/**
 * 发送验证码
 * @param {string} email 邮箱地址
 * @param {string} type 验证码类型（login, register, reset_password）
 * @returns {Promise} 请求结果
 */
export async function sendCode(email, type = "login") {
  try {
    const response = await httpClient.post(API_ENDPOINTS.AUTH.SEND_CODE, {
      email,
      type,
    });
    return response;
  } catch (error) {
    console.error("发送验证码失败:", error);
    throw error.response?.data || { success: false, error: "发送验证码失败" };
  }
}

/**
 * 验证码登录
 * @param {string} email 邮箱地址
 * @param {string} code 验证码
 * @param {string} type 验证码类型（login, register, reset_password）
 * @returns {Promise} 请求结果
 */
export async function verifyCode(email, code, type = "login") {
  try {
    const response = await httpClient.post(API_ENDPOINTS.AUTH.VERIFY_CODE, {
      email,
      code,
      type,
    });

    // 如果登录成功，保存令牌和用户信息
    if (response.success) {
      saveToken(response.token);
      saveUser(response.user);
    }

    return response;
  } catch (error) {
    console.error("验证码验证失败:", error);
    throw error.response?.data || { success: false, error: "验证码验证失败" };
  }
}

/**
 * 获取当前用户信息
 * @returns {Promise} 请求结果
 */
export async function getCurrentUser() {
  try {
    const token = getToken();
    if (!token) {
      return { success: false, error: "未登录" };
    }

    const response = await httpClient.get(API_ENDPOINTS.AUTH.CURRENT_USER);
    return response;
  } catch (error) {
    console.error("获取用户信息失败:", error);
    if (error.response?.status === 401) {
      // 如果令牌无效，清除本地存储
      clearAuth();
    }
    throw error.response?.data || { success: false, error: "获取用户信息失败" };
  }
}

/**
 * 更新用户信息
 * @param {Object} userData 用户数据
 * @returns {Promise} 请求结果
 */
export async function updateUser(userData) {
  try {
    const token = getToken();
    if (!token) {
      return { success: false, error: "未登录" };
    }

    const response = await httpClient.put(
      API_ENDPOINTS.AUTH.UPDATE_USER,
      userData
    );

    // 更新本地存储的用户信息
    if (response.success) {
      saveUser(response.user);
    }

    return response;
  } catch (error) {
    console.error("更新用户信息失败:", error);
    throw error.response?.data || { success: false, error: "更新用户信息失败" };
  }
}

/**
 * 登出
 */
export function logout() {
  clearAuth();
  // 可以添加重定向到登录页面的逻辑
  window.location.href = "/";
}

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export function isLoggedIn() {
  return !!getToken();
}

/**
 * 获取当前用户
 * @returns {Object|null} 用户信息
 */
export function getUser() {
  try {
    const userJson = localStorage.getItem(USER_KEY);
    return userJson ? JSON.parse(userJson) : null;
  } catch (error) {
    console.error("获取用户信息失败:", error);
    return null;
  }
}

/**
 * 获取认证令牌
 * @returns {string|null} 认证令牌
 */
export function getToken() {
  return localStorage.getItem(TOKEN_KEY);
}

/**
 * 保存认证令牌
 * @param {string} token 认证令牌
 */
function saveToken(token) {
  localStorage.setItem(TOKEN_KEY, token);
}

/**
 * 保存用户信息
 * @param {Object} user 用户信息
 */
function saveUser(user) {
  localStorage.setItem(USER_KEY, JSON.stringify(user));
}

/**
 * 清除认证信息
 */
export function clearAuth() {
  localStorage.removeItem(TOKEN_KEY);
  localStorage.removeItem(USER_KEY);
}

/**
 * 设置全局请求拦截器（已废弃，使用统一的HTTP客户端）
 * @deprecated 使用 httpClient 代替
 */
export function setupAuthInterceptors() {
  console.warn("setupAuthInterceptors 已废弃，请使用统一的 httpClient");
}

export default {
  sendCode,
  verifyCode,
  getCurrentUser,
  updateUser,
  logout,
  isLoggedIn,
  getUser,
  getToken,
  clearAuth,
  setupAuthInterceptors,
};
