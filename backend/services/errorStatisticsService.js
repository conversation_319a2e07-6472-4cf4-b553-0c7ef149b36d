const crypto = require("crypto");
const { Op } = require("sequelize");
const db = require("../models");
const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorStatistic } = db;

/**
 * 错误统计服务
 */
class ErrorStatisticsService {
  /**
   * 生成错误唯一标识
   * @param {string} message - 错误消息
   * @param {string} stack - 错误堆栈
   * @param {string} errorType - 错误类型
   * @returns {string} 错误哈希值
   */
  generateErrorHash(message, stack, errorType) {
    const content = `${errorType}:${message}:${stack || ""}`;
    return crypto.createHash("md5").update(content).digest("hex");
  }

  /**
   * 解析用户代理信息
   * @param {string} userAgent - 用户代理字符串
   * @returns {Object} 解析后的浏览器和设备信息
   */
  parseUserAgent(userAgent) {
    if (!userAgent) return { browser: {}, device: {} };

    // 简单的用户代理解析（可以使用更专业的库如ua-parser-js）
    const browser = {};
    const device = {};

    // 检测浏览器
    if (userAgent.includes("Chrome")) {
      browser.name = "Chrome";
      const match = userAgent.match(/Chrome\/(\d+)/);
      if (match) browser.version = match[1];
    } else if (userAgent.includes("Firefox")) {
      browser.name = "Firefox";
      const match = userAgent.match(/Firefox\/(\d+)/);
      if (match) browser.version = match[1];
    } else if (userAgent.includes("Safari")) {
      browser.name = "Safari";
      const match = userAgent.match(/Version\/(\d+)/);
      if (match) browser.version = match[1];
    }

    // 检测操作系统
    if (userAgent.includes("Windows")) {
      device.os = "Windows";
    } else if (userAgent.includes("Mac OS")) {
      device.os = "macOS";
    } else if (userAgent.includes("Linux")) {
      device.os = "Linux";
    } else if (userAgent.includes("Android")) {
      device.os = "Android";
    } else if (userAgent.includes("iOS")) {
      device.os = "iOS";
    }

    // 检测设备类型
    if (userAgent.includes("Mobile")) {
      device.type = "mobile";
    } else if (userAgent.includes("Tablet")) {
      device.type = "tablet";
    } else {
      device.type = "desktop";
    }

    return { browser, device };
  }

  /**
   * 记录错误
   * @param {Object} errorData - 错误数据
   * @returns {Promise<Object>} 处理结果
   */
  async recordError(errorData) {
    const {
      error_type,
      message,
      stack,
      url,
      line_number,
      column_number,
      filename,
      user_agent,
      user_id,
      session_id,
      ip_address,
      context_data,
      enhanced_context,
    } = errorData;

    // 生成错误哈希
    const error_hash = this.generateErrorHash(message, stack, error_type);

    // 解析用户代理
    const { browser, device } = this.parseUserAgent(user_agent);

    // 提取严重程度信息
    const severity = enhanced_context?.severity || {};
    const severity_level = severity.level || null;
    const severity_score = severity.score || null;

    try {
      // 记录详细错误日志
      await ErrorLog.create({
        error_hash,
        error_type,
        message,
        stack,
        url,
        line_number,
        column_number,
        filename,
        user_agent,
        user_id,
        session_id,
        ip_address,
        browser_info: browser,
        device_info: device,
        context_data,
        enhanced_context,
        severity_level,
        severity_score,
      });

      // 更新或创建错误汇总
      await this.updateErrorSummary(error_hash, {
        error_type,
        message,
        stack,
        user_id,
      });

      // 更新每日统计
      await this.updateDailyStatistics(error_hash, error_type, user_id);

      return { success: true, error_hash };
    } catch (error) {
      console.error("记录错误失败:", error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 批量记录错误
   * @param {Array} errorList - 错误列表
   * @returns {Promise<Object>} 处理结果
   */
  async recordErrors(errorList) {
    const results = [];

    for (const errorData of errorList) {
      const result = await this.recordError(errorData);
      results.push(result);
    }

    const successCount = results.filter((r) => r.success).length;
    const failCount = results.length - successCount;

    return {
      total: results.length,
      success: successCount,
      failed: failCount,
      results,
    };
  }

  /**
   * 更新错误汇总
   * @param {string} errorHash - 错误哈希
   * @param {Object} errorInfo - 错误信息
   */
  async updateErrorSummary(errorHash, errorInfo) {
    const { error_type, message, stack, user_id } = errorInfo;
    const now = new Date();

    const [summary, created] = await ErrorSummary.findOrCreate({
      where: { error_hash: errorHash },
      defaults: {
        error_hash: errorHash,
        error_type,
        message,
        stack_sample: stack ? stack.substring(0, 1000) : null, // 限制堆栈长度
        first_seen: now,
        last_seen: now,
        occurrence_count: 1,
        affected_users: user_id ? 1 : 0,
      },
    });

    if (!created) {
      // 更新现有记录
      const updateData = {
        last_seen: now,
        occurrence_count: summary.occurrence_count + 1,
      };

      // 如果有用户ID，更新影响用户数（这里简化处理，实际应该去重计算）
      if (user_id) {
        updateData.affected_users = summary.affected_users + 1;
      }

      await summary.update(updateData);
    }
  }

  /**
   * 更新每日统计
   * @param {string} errorHash - 错误哈希
   * @param {string} errorType - 错误类型
   * @param {string} userId - 用户ID
   */
  async updateDailyStatistics(errorHash, errorType, userId) {
    const today = new Date().toISOString().split("T")[0]; // YYYY-MM-DD格式

    const [statistic, created] = await ErrorStatistic.findOrCreate({
      where: {
        date: today,
        error_hash: errorHash,
      },
      defaults: {
        date: today,
        error_hash: errorHash,
        error_type: errorType,
        occurrence_count: 1,
        affected_users: userId ? 1 : 0,
      },
    });

    if (!created) {
      const updateData = {
        occurrence_count: statistic.occurrence_count + 1,
      };

      if (userId) {
        updateData.affected_users = statistic.affected_users + 1;
      }

      await statistic.update(updateData);
    }
  }

  /**
   * 获取错误汇总列表
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 错误汇总列表
   */
  async getErrorSummaries(options = {}) {
    const {
      page = 1,
      limit = 20,
      error_type,
      status,
      priority,
      severity_level,
      start_date,
      end_date,
      order_by = "last_seen",
      order_direction = "DESC",
    } = options;

    const where = {};
    const include = [];

    if (error_type) where.error_type = error_type;
    if (status) where.status = status;
    if (priority) where.priority = priority;

    if (start_date || end_date) {
      where.last_seen = {};
      if (start_date) where.last_seen[Op.gte] = new Date(start_date);
      if (end_date) where.last_seen[Op.lte] = new Date(end_date);
    }

    // 如果需要按严重程度筛选，需要关联ErrorLog表
    if (severity_level) {
      include.push({
        model: ErrorLog,
        as: "logs",
        where: { severity_level },
        attributes: ["severity_level", "severity_score"],
        required: true,
        limit: 1,
        order: [["created_at", "DESC"]],
      });
    } else {
      // 即使不筛选，也获取最新的严重程度信息用于显示
      include.push({
        model: ErrorLog,
        as: "logs",
        attributes: ["severity_level", "severity_score"],
        required: false,
        limit: 1,
        order: [["created_at", "DESC"]],
      });
    }

    const offset = (page - 1) * limit;

    const result = await ErrorSummary.findAndCountAll({
      where,
      include,
      limit,
      offset,
      order: [[order_by, order_direction]],
      distinct: true, // 确保count正确
    });

    // 为每个汇总添加最新的严重程度信息
    const dataWithSeverity = result.rows.map((summary) => {
      const summaryData = summary.toJSON();
      if (summaryData.logs && summaryData.logs.length > 0) {
        summaryData.severity_level = summaryData.logs[0].severity_level;
        summaryData.severity_score = summaryData.logs[0].severity_score;
      }
      delete summaryData.logs; // 移除logs字段，避免冗余
      return summaryData;
    });

    return {
      data: dataWithSeverity,
      total: result.count,
      page,
      limit,
      totalPages: Math.ceil(result.count / limit),
    };
  }

  /**
   * 获取错误详情
   * @param {string} errorHash - 错误哈希
   * @returns {Promise<Object>} 错误详情
   */
  async getErrorDetails(errorHash) {
    const summary = await ErrorSummary.findOne({
      where: { error_hash: errorHash },
    });

    if (!summary) {
      throw new Error("错误不存在");
    }

    // 获取最近的错误日志示例
    const recentLogs = await ErrorLog.findAll({
      where: { error_hash: errorHash },
      order: [["created_at", "DESC"]],
      limit: 10,
    });

    return {
      summary,
      recent_logs: recentLogs,
    };
  }

  /**
   * 更新错误状态
   * @param {string} errorHash - 错误哈希
   * @param {string} status - 新状态
   * @param {string} userId - 操作用户ID
   * @returns {Promise<Object>} 更新结果
   */
  async updateErrorStatus(errorHash, status, userId) {
    const summary = await ErrorSummary.findOne({
      where: { error_hash: errorHash },
    });

    if (!summary) {
      throw new Error("错误不存在");
    }

    await summary.update({
      status,
      assigned_to: userId,
    });

    return { success: true };
  }

  /**
   * 获取错误统计数据
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 统计数据
   */
  async getStatistics(options = {}) {
    const { start_date, end_date, error_type, group_by = "date" } = options;

    const where = {};

    if (error_type) where.error_type = error_type;

    if (start_date || end_date) {
      where.date = {};
      if (start_date) where.date[Op.gte] = start_date;
      if (end_date) where.date[Op.lte] = end_date;
    }

    // 根据分组方式返回不同的统计数据
    if (group_by === "date") {
      return await this.getStatisticsByDate(where);
    } else if (group_by === "type") {
      return await this.getStatisticsByType(where);
    }

    return {};
  }

  /**
   * 按日期分组统计
   * @param {Object} where - 查询条件
   * @returns {Promise<Array>} 统计结果
   */
  async getStatisticsByDate(where) {
    const { sequelize } = require("../models");

    const results = await ErrorStatistic.findAll({
      where,
      attributes: [
        "date",
        [
          sequelize.fn("SUM", sequelize.col("occurrence_count")),
          "total_occurrences",
        ],
        [
          sequelize.fn("SUM", sequelize.col("affected_users")),
          "total_affected_users",
        ],
      ],
      group: ["date"],
      order: [["date", "ASC"]],
    });

    return results;
  }

  /**
   * 按错误类型分组统计
   * @param {Object} where - 查询条件
   * @returns {Promise<Array>} 统计结果
   */
  async getStatisticsByType(where) {
    const { sequelize } = require("../models");

    const results = await ErrorStatistic.findAll({
      where,
      attributes: [
        "error_type",
        [
          sequelize.fn("SUM", sequelize.col("occurrence_count")),
          "total_occurrences",
        ],
        [
          sequelize.fn("SUM", sequelize.col("affected_users")),
          "total_affected_users",
        ],
      ],
      group: ["error_type"],
      order: [[sequelize.fn("SUM", sequelize.col("occurrence_count")), "DESC"]],
    });

    return results;
  }
}

module.exports = new ErrorStatisticsService();
