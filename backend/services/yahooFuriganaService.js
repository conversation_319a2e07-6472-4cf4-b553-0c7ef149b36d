/**
 * Yahoo! Japan Jlp API Furigana服务
 * 用于获取日语文本的假名标注
 */
const axios = require("axios");

// Yahoo API配置
const YAHOO_CLIENT_ID =
  process.env.YAHOO_CLIENT_ID ||
  "dj00aiZpPWgwdmhnSk84c1R6MiZzPWNvbnN1bWVyc2VjcmV0Jng9YzY-";
const YAHOO_FURIGANA_API_URL =
  "https://jlp.yahooapis.jp/FuriganaService/V2/furigana";

/**
 * 调用Yahoo Furigana API获取假名标注
 * @param {string} text - 需要标注的文本
 * @returns {Promise<Object>} - 标注结果
 */
async function getFurigana(text) {
  try {
    console.log(
      `调用Yahoo Furigana API获取标注: ${text.substring(0, 30)}${
        text.length > 30 ? "..." : ""
      }`
    );

    const response = await axios.post(
      YAHOO_FURIGANA_API_URL,
      {
        id: "1",
        jsonrpc: "2.0",
        method: "jlp.furiganaservice.furigana",
        params: {
          q: text,
          grade: 1,
          mode: "furigana",
          subword_flag: 1, // 启用字符级分解
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
          "User-Agent": "Yahoo AppID: " + YAHOO_CLIENT_ID,
        },
      }
    );

    console.log("Yahoo Furigana API响应状态:", response.status);

    // 检查响应
    if (response.data && response.data.result && response.data.result.word) {
      return response.data.result;
    } else {
      console.error("Yahoo Furigana API返回格式不正确:", response.data);
      throw new Error("标注API返回格式不正确");
    }
  } catch (error) {
    console.error("调用Yahoo Furigana API失败:", error.message);
    if (error.response) {
      console.error("错误响应:", error.response.data);
      console.error("状态码:", error.response.status);
    }
    throw error;
  }
}

/**
 * 批量获取假名标注
 * @param {Array} segments - 文本段落数组，每个元素包含id和content
 * @returns {Promise<Object>} - 标注结果，格式为 {id: 标注结果}
 */
async function batchGetFurigana(segments) {
  try {
    console.log(`批量获取假名标注，段落数: ${segments.length}`);

    // 创建结果对象
    const results = {};

    // 处理每个段落
    for (const segment of segments) {
      if (!segment.content || segment.content.trim() === "") {
        results[segment.id] = "";
        continue;
      }

      try {
        // 调用API获取标注
        const furiganaResult = await getFurigana(segment.content);

        // 处理标注结果
        const processedResult = processFuriganaResult(furiganaResult);

        // 存储结果
        results[segment.id] = {
          reading: processedResult.reading,
          characters: processedResult.characters,
        };
      } catch (error) {
        console.error(`处理段落 ${segment.id} 失败:`, error.message);
        // 失败时返回原文
        results[segment.id] = {
          reading: segment.content,
          characters: [],
        };
      }
    }

    return results;
  } catch (error) {
    console.error("批量获取假名标注失败:", error.message);
    throw error;
  }
}

/**
 * 处理Yahoo Furigana API返回的结果
 * @param {Object} result - API返回的结果
 * @returns {Object} - 处理后的结果，包含reading和characters
 */
function processFuriganaResult(result) {
  try {
    // 初始化结果
    const processedResult = {
      reading: "",
      characters: [],
    };

    // 处理每个词
    if (result.word && Array.isArray(result.word)) {
      for (const word of result.word) {
        // 添加到总读音
        processedResult.reading += word.furigana || word.surface;

        // 处理字符级标注
        if (word.subword && Array.isArray(word.subword)) {
          for (const subword of word.subword) {
            processedResult.characters.push({
              char: subword.surface,
              reading: subword.furigana || subword.surface,
              isEdited: false,
            });
          }
        } else {
          // 如果没有subword，将整个词作为一个字符
          processedResult.characters.push({
            char: word.surface,
            reading: word.furigana || word.surface,
            isEdited: false,
          });
        }
      }
    }

    return processedResult;
  } catch (error) {
    console.error("处理标注结果失败:", error.message);
    throw error;
  }
}

module.exports = {
  getFurigana,
  batchGetFurigana,
};
