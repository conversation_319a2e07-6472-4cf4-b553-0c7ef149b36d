/**
 * 用户反馈服务
 * 处理用户反馈的提交和管理
 */
const db = require("../models");
const { nanoid } = require("nanoid");
const UAParser = require("ua-parser-js");

/**
 * 创建用户反馈
 * @param {Object} feedbackData 反馈数据
 * @param {string} [feedbackData.userId] 用户ID（可选）
 * @param {string} feedbackData.type 反馈类型
 * @param {string} feedbackData.content 反馈内容
 * @param {string} [feedbackData.contact] 联系方式（可选）
 * @param {string} [feedbackData.pageUrl] 页面URL（可选）
 * @param {Object} req 请求对象，用于获取用户代理等信息
 * @returns {Promise<Object>} 创建结果
 */
async function createFeedback(feedbackData, req) {
  try {
    // 解析用户代理信息
    const userAgent = req.headers["user-agent"];
    const parser = new UAParser(userAgent);
    const browserInfo = parser.getBrowser();
    const deviceInfo = parser.getDevice();
    const osInfo = parser.getOS();

    // 获取客户端IP
    const ipAddress =
      req.headers["x-forwarded-for"] || req.connection.remoteAddress;

    // 创建反馈记录
    const feedback = await db.Feedback.create({
      userId: feedbackData.userId || null,
      type: feedbackData.type,
      content: feedbackData.content,
      contact: feedbackData.contact || null,
      pageUrl: feedbackData.pageUrl || null,
      deviceInfo: JSON.stringify({
        ...deviceInfo,
        os: osInfo,
        ip: ipAddress,
      }),
      browserInfo: JSON.stringify(browserInfo),
      status: "pending",
    });

    return {
      success: true,
      feedback: {
        id: feedback.id,
        type: feedback.type,
        content: feedback.content,
        status: feedback.status,
        createdAt: feedback.createdAt,
      },
    };
  } catch (error) {
    console.error("创建用户反馈失败:", error);
    return {
      success: false,
      error: `创建用户反馈失败: ${error.message}`,
    };
  }
}

/**
 * 获取用户反馈列表
 * @param {Object} options 查询选项
 * @param {number} [options.page=1] 页码
 * @param {number} [options.pageSize=10] 每页数量
 * @param {string} [options.userId] 用户ID（可选，用于筛选特定用户的反馈）
 * @param {string} [options.status] 状态（可选，用于筛选特定状态的反馈）
 * @param {string} [options.type] 类型（可选，用于筛选特定类型的反馈）
 * @returns {Promise<Object>} 查询结果
 */
async function getFeedbacks(options = {}) {
  try {
    const page = options.page || 1;
    const pageSize = options.pageSize || 10;
    const offset = (page - 1) * pageSize;

    // 构建查询条件
    const where = {};
    if (options.userId) where.userId = options.userId;
    if (options.status) where.status = options.status;
    if (options.type) where.type = options.type;

    // 查询反馈列表
    const { count, rows } = await db.Feedback.findAndCountAll({
      where,
      limit: pageSize,
      offset,
      order: [["created_at", "DESC"]],
      include: [
        {
          model: db.User,
          as: "user",
          attributes: ["id", "username", "email"],
        },
        {
          model: db.User,
          as: "processor",
          attributes: ["id", "username", "email"],
        },
      ],
    });

    return {
      success: true,
      feedbacks: rows,
      pagination: {
        total: count,
        page,
        pageSize,
        totalPages: Math.ceil(count / pageSize),
      },
    };
  } catch (error) {
    console.error("获取用户反馈列表失败:", error);
    return {
      success: false,
      error: `获取用户反馈列表失败: ${error.message}`,
    };
  }
}

/**
 * 更新反馈状态
 * @param {number} id 反馈ID
 * @param {Object} updateData 更新数据
 * @param {string} updateData.status 状态
 * @param {string} [updateData.adminReply] 管理员回复（可选）
 * @param {string} adminId 管理员ID
 * @returns {Promise<Object>} 更新结果
 */
async function updateFeedbackStatus(id, updateData, adminId) {
  try {
    // 查找反馈
    const feedback = await db.Feedback.findByPk(id);
    if (!feedback) {
      return {
        success: false,
        error: "反馈不存在",
      };
    }

    // 更新反馈
    await feedback.update({
      status: updateData.status,
      adminReply: updateData.adminReply || feedback.adminReply,
      processedBy: adminId,
      processedAt: new Date(),
    });

    return {
      success: true,
      feedback,
    };
  } catch (error) {
    console.error("更新反馈状态失败:", error);
    return {
      success: false,
      error: `更新反馈状态失败: ${error.message}`,
    };
  }
}

module.exports = {
  createFeedback,
  getFeedbacks,
  updateFeedbackStatus,
};
