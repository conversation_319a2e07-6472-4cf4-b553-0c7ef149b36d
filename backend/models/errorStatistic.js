module.exports = (sequelize, DataTypes) => {
  const ErrorStatistic = sequelize.define(
    "ErrorStatistic",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        comment: "统计日期",
      },
      error_hash: {
        type: DataTypes.STRING(32),
        allowNull: false,
        comment: "错误标识",
      },
      error_type: {
        type: DataTypes.ENUM(
          "javascript",
          "vue",
          "network",
          "resource",
          "promise"
        ),
        allowNull: false,
      },
      occurrence_count: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: "当日发生次数",
      },
      affected_users: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: "当日影响用户数",
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      tableName: "error_statistics",
      timestamps: false,
      indexes: [
        {
          fields: ["date", "error_hash"],
          unique: true,
        },
        { fields: ["date"] },
        { fields: ["error_type"] },
      ],
    }
  );

  return ErrorStatistic;
};
