module.exports = (sequelize, DataTypes) => {
  const ErrorSummary = sequelize.define(
    "ErrorSummary",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      error_hash: {
        type: DataTypes.STRING(32),
        allowNull: false,
        unique: true,
        comment: "错误唯一标识",
      },
      error_type: {
        type: DataTypes.ENUM(
          "javascript",
          "vue",
          "network",
          "resource",
          "promise"
        ),
        allowNull: false,
      },
      message: {
        type: DataTypes.TEXT,
        allowNull: false,
        comment: "错误消息",
      },
      stack_sample: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "错误堆栈示例",
      },
      first_seen: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: "首次发现时间",
      },
      last_seen: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: "最后发现时间",
      },
      occurrence_count: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 1,
        comment: "发生次数",
      },
      affected_users: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: "影响用户数",
      },
      status: {
        type: DataTypes.ENUM("new", "acknowledged", "resolved", "ignored"),
        allowNull: false,
        defaultValue: "new",
        comment: "处理状态",
      },
      priority: {
        type: DataTypes.ENUM("low", "medium", "high", "critical"),
        allowNull: false,
        defaultValue: "medium",
        comment: "优先级",
      },
      assigned_to: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: "分配给谁处理",
      },
      resolution_notes: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "解决方案备注",
      },
    },
    {
      tableName: "error_summaries",
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
      indexes: [
        { fields: ["error_type"] },
        { fields: ["status"] },
        { fields: ["priority"] },
        { fields: ["first_seen"] },
        { fields: ["last_seen"] },
      ],
    }
  );

  // 定义关联关系
  ErrorSummary.associate = function (models) {
    // 一个错误汇总对应多个错误日志
    ErrorSummary.hasMany(models.ErrorLog, {
      foreignKey: "error_hash",
      sourceKey: "error_hash",
      as: "logs",
    });
  };

  return ErrorSummary;
};
