/**
 * 安全头部中间件
 * 添加各种安全相关的HTTP头部，增强应用安全性
 */
const helmet = require('helmet');

/**
 * 创建安全头部中间件
 * @param {Object} options 配置选项
 * @returns {Function} Express中间件
 */
function createSecurityHeaders(options = {}) {
  const {
    // 内容安全策略
    contentSecurityPolicy = {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'", "https:"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
      },
    },
    // XSS保护
    xssFilter = true,
    // 禁止嗅探MIME类型
    noSniff = true,
    // 点击劫持保护
    frameguard = {
      action: 'deny',
    },
    // HSTS
    hsts = {
      maxAge: 15552000, // 180天
      includeSubDomains: true,
      preload: true,
    },
    // 禁止DNS预取
    dnsPrefetchControl = {
      allow: false,
    },
    // 引用策略
    referrerPolicy = {
      policy: 'strict-origin-when-cross-origin',
    },
    // 禁止缓存
    noCache = false,
  } = options;

  // 创建Helmet中间件
  return helmet({
    contentSecurityPolicy,
    xssFilter,
    noSniff,
    frameguard,
    hsts,
    dnsPrefetchControl,
    referrerPolicy,
    ...(noCache ? { noCache: true } : {}),
  });
}

// 预定义的安全头部中间件
const securityHeaders = createSecurityHeaders();

// 针对API的安全头部中间件（更宽松的CSP）
const apiSecurityHeaders = createSecurityHeaders({
  contentSecurityPolicy: false, // API不需要CSP
  noCache: true, // API响应不应被缓存
});

module.exports = {
  securityHeaders,
  apiSecurityHeaders,
};
