/**
 * 认证控制器
 * 处理认证相关的请求
 * 增强安全性，防止暴力破解和接口滥用
 */
const authService = require("../services/authService");
const db = require("../models");
const { addToDynamicBlacklist } = require("../middleware/ipFilterMiddleware");
const { validate, schemas } = require("../middleware/validationMiddleware");

/**
 * 发送验证码
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
async function sendCode(req, res) {
  try {
    const { email, type = "login" } = req.body;

    // 验证邮箱格式
    if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return res.status(400).json({
        success: false,
        error: "无效的邮箱地址",
        code: "INVALID_EMAIL",
      });
    }

    // 验证类型
    if (!["login", "register", "reset_password"].includes(type)) {
      return res.status(400).json({
        success: false,
        error: "无效的验证码类型",
        code: "INVALID_CODE_TYPE",
      });
    }

    // 获取客户端IP和请求ID
    const ip =
      req.ip || req.headers["x-forwarded-for"] || req.connection.remoteAddress;
    const requestId = req.id || "unknown";

    // 记录请求信息
    console.log(
      `[Auth] 发送验证码请求: IP=${ip}, Email=${email}, Type=${type}, RequestID=${requestId}`
    );

    // 发送验证码
    const result = await authService.sendCode(email, type, ip);

    if (!result.success) {
      // 记录失败信息
      console.warn(
        `[Auth] 发送验证码失败: IP=${ip}, Email=${email}, Error=${result.error}, RequestID=${requestId}`
      );

      // 如果是因为请求过于频繁，考虑将IP添加到动态黑名单
      if (result.error.includes("请求过于频繁")) {
        addToDynamicBlacklist(ip, 30 * 60 * 1000, "验证码请求过于频繁"); // 30分钟
      }

      return res.status(400).json({
        ...result,
        code: "SEND_CODE_FAILED",
      });
    }

    res.json(result);
  } catch (error) {
    console.error("发送验证码失败:", error);
    res.status(500).json({
      success: false,
      error: `发送验证码失败: ${error.message}`,
      code: "INTERNAL_ERROR",
    });
  }
}

/**
 * 验证码登录
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
async function verifyCode(req, res) {
  try {
    const { email, code, type = "login" } = req.body;

    // 验证邮箱格式
    if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return res.status(400).json({
        success: false,
        error: "无效的邮箱地址",
        code: "INVALID_EMAIL",
      });
    }

    // 验证验证码格式
    if (!code || !/^\d{6}$/.test(code)) {
      return res.status(400).json({
        success: false,
        error: "无效的验证码",
        code: "INVALID_CODE",
      });
    }

    // 验证类型
    if (!["login", "register", "reset_password"].includes(type)) {
      return res.status(400).json({
        success: false,
        error: "无效的验证码类型",
        code: "INVALID_CODE_TYPE",
      });
    }

    // 获取客户端IP和请求ID
    const ip =
      req.ip || req.headers["x-forwarded-for"] || req.connection.remoteAddress;
    const requestId = req.id || "unknown";
    const userAgent = req.headers["user-agent"] || "unknown";

    // 记录请求信息
    console.log(
      `[Auth] 验证码登录请求: IP=${ip}, Email=${email}, Type=${type}, RequestID=${requestId}`
    );

    // 验证验证码
    const result = await authService.verifyCode(email, code, type, ip, req);

    if (!result.success) {
      // 记录失败信息
      console.warn(
        `[Auth] 验证码验证失败: IP=${ip}, Email=${email}, Error=${result.error}, RequestID=${requestId}`
      );

      // 如果是验证码错误，可能是暴力破解尝试
      if (
        result.error.includes("验证码无效") ||
        result.error.includes("验证码已过期")
      ) {
        // 记录失败次数，如果短时间内多次失败，添加到黑名单
        const failCount = await db.FailedLogin.count({
          where: {
            ip,
            createdAt: {
              [db.Sequelize.Op.gt]: new Date(Date.now() - 30 * 60 * 1000), // 30分钟内
            },
          },
        });

        // 记录本次失败
        await db.FailedLogin.create({
          ip,
          email,
          userAgent,
          reason: result.error,
        });

        // 如果30分钟内失败5次以上，添加到黑名单
        if (failCount >= 4) {
          // 这是第5次
          addToDynamicBlacklist(ip, 60 * 60 * 1000, "验证码验证多次失败"); // 1小时
          console.warn(`[Auth] IP ${ip} 因多次验证失败被添加到黑名单`);
        }
      }

      return res.status(400).json({
        ...result,
        code: "VERIFY_CODE_FAILED",
      });
    }

    // 登录成功，清除该IP的失败记录
    await db.FailedLogin.destroy({
      where: { ip },
    });

    // 记录成功登录
    console.log(
      `[Auth] 用户登录成功: IP=${ip}, Email=${email}, UserID=${result.user?.id}, RequestID=${requestId}`
    );

    res.json(result);
  } catch (error) {
    console.error("验证码验证失败:", error);
    res.status(500).json({
      success: false,
      error: `验证码验证失败: ${error.message}`,
      code: "INTERNAL_ERROR",
    });
  }
}

/**
 * 获取当前用户信息
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
async function getCurrentUser(req, res) {
  try {
    // 从请求对象中获取用户ID
    const userId = req.user.id;

    // 查询用户信息
    const user = await db.User.findByPk(userId, {
      attributes: [
        "id",
        "email",
        "username",
        "role",
        "status",
        "settings",
        "lastLoginAt",
      ],
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: "用户不存在",
      });
    }

    res.json({
      success: true,
      user,
    });
  } catch (error) {
    console.error("获取当前用户信息失败:", error);
    res.status(500).json({
      success: false,
      error: `获取当前用户信息失败: ${error.message}`,
    });
  }
}

/**
 * 更新用户信息
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
async function updateUser(req, res) {
  try {
    // 从请求对象中获取用户ID
    const userId = req.user.id;
    const { username, settings } = req.body;

    // 查询用户
    const user = await db.User.findByPk(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        error: "用户不存在",
      });
    }

    // 更新用户信息
    await user.update({
      username: username || user.username,
      settings: settings || user.settings,
    });

    res.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        role: user.role,
        status: user.status,
        settings: user.settings,
        lastLoginAt: user.lastLoginAt,
      },
      message: "用户信息更新成功",
    });
  } catch (error) {
    console.error("更新用户信息失败:", error);
    res.status(500).json({
      success: false,
      error: `更新用户信息失败: ${error.message}`,
    });
  }
}

module.exports = {
  sendCode,
  verifyCode,
  getCurrentUser,
  updateUser,
};
