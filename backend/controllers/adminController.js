/**
 * 管理员控制器
 * 处理管理员相关的请求
 */
const db = require("../models");
const adminLogService = require("../services/adminLogService");

/**
 * 获取用户列表
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
async function getUsers(req, res) {
  try {
    const {
      page = 1,
      limit = 20,
      search = "",
      status,
      role,
      sortBy = "created_at",
      sortOrder = "DESC",
    } = req.query;

    // 构建查询条件
    const where = {};
    if (search) {
      where[db.Sequelize.Op.or] = [
        { email: { [db.Sequelize.Op.like]: `%${search}%` } },
        { username: { [db.Sequelize.Op.like]: `%${search}%` } },
      ];
    }

    if (status) {
      where.status = status;
    }

    if (role) {
      where.role = role;
    }

    // 构建排序条件
    const order = [[sortBy, sortOrder]];

    // 查询用户
    const { count, rows } = await db.User.findAndCountAll({
      where,
      attributes: { exclude: ["settings"] }, // 排除敏感信息
      order,
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit),
    });

    res.json({
      success: true,
      data: {
        total: count,
        users: rows,
        page: parseInt(page),
        limit: parseInt(limit),
      },
    });
  } catch (error) {
    console.error("获取用户列表失败:", error);
    res.status(500).json({
      success: false,
      error: `获取用户列表失败: ${error.message}`,
    });
  }
}

/**
 * 获取用户详情
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
async function getUserDetail(req, res) {
  try {
    const { id } = req.params;

    // 查询用户
    const user = await db.User.findByPk(id, {
      attributes: { exclude: ["settings"] }, // 排除敏感信息
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: "用户不存在",
      });
    }

    // 查询用户的内容数量
    const contentCount = await db.Content.count({
      where: { userId: id },
    });

    res.json({
      success: true,
      data: {
        user,
        stats: {
          contentCount,
        },
      },
    });
  } catch (error) {
    console.error("获取用户详情失败:", error);
    res.status(500).json({
      success: false,
      error: `获取用户详情失败: ${error.message}`,
    });
  }
}

/**
 * 更新用户状态
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
async function updateUserStatus(req, res) {
  try {
    const { id } = req.params;
    const { status } = req.body;

    // 验证状态
    if (!["active", "inactive", "banned"].includes(status)) {
      return res.status(400).json({
        success: false,
        error: "无效的状态值",
      });
    }

    // 查询用户
    const user = await db.User.findByPk(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        error: "用户不存在",
      });
    }

    // 不允许修改自己的状态
    if (user.id === req.admin.id) {
      return res.status(400).json({
        success: false,
        error: "不能修改自己的状态",
      });
    }

    // 更新用户状态
    await user.update({ status });

    res.json({
      success: true,
      message: "用户状态更新成功",
      data: {
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          role: user.role,
          status: user.status,
        },
      },
    });
  } catch (error) {
    console.error("更新用户状态失败:", error);
    res.status(500).json({
      success: false,
      error: `更新用户状态失败: ${error.message}`,
    });
  }
}

/**
 * 更新用户角色
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
async function updateUserRole(req, res) {
  try {
    const { id } = req.params;
    const { role } = req.body;

    // 验证角色
    if (!["user", "admin"].includes(role)) {
      return res.status(400).json({
        success: false,
        error: "无效的角色值",
      });
    }

    // 查询用户
    const user = await db.User.findByPk(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        error: "用户不存在",
      });
    }

    // 不允许修改自己的角色
    if (user.id === req.admin.id) {
      return res.status(400).json({
        success: false,
        error: "不能修改自己的角色",
      });
    }

    // 更新用户角色
    await user.update({ role });

    res.json({
      success: true,
      message: "用户角色更新成功",
      data: {
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          role: user.role,
          status: user.status,
        },
      },
    });
  } catch (error) {
    console.error("更新用户角色失败:", error);
    res.status(500).json({
      success: false,
      error: `更新用户角色失败: ${error.message}`,
    });
  }
}

/**
 * 获取管理员操作日志
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
async function getAdminLogs(req, res) {
  try {
    const {
      page,
      limit,
      adminId,
      action,
      targetType,
      targetId,
      startDate,
      endDate,
    } = req.query;

    // 获取日志
    const result = await adminLogService.getAdminLogs({
      page,
      limit,
      adminId,
      action,
      targetType,
      targetId,
      startDate,
      endDate,
    });

    if (!result.success) {
      return res.status(500).json(result);
    }

    res.json({
      success: true,
      data: {
        total: result.total,
        logs: result.logs,
        page: result.page,
        limit: result.limit,
      },
    });
  } catch (error) {
    console.error("获取管理员操作日志失败:", error);
    res.status(500).json({
      success: false,
      error: `获取管理员操作日志失败: ${error.message}`,
    });
  }
}

/**
 * 获取系统概览数据
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
async function getDashboardStats(req, res) {
  try {
    // 用户统计
    const totalUsers = await db.User.count();
    const activeUsers = await db.User.count({ where: { status: "active" } });
    const adminUsers = await db.User.count({ where: { role: "admin" } });

    // 内容统计
    const totalContents = await db.Content.count();
    const publishedContents = await db.Content.count({
      where: { status: "published" },
    });

    // 最近注册的用户
    const recentUsers = await db.User.findAll({
      attributes: ["id", "email", "username", "role", "status", "created_at"],
      order: [["created_at", "DESC"]],
      limit: 5,
    });

    // 最近创建的内容
    const recentContents = await db.Content.findAll({
      attributes: ["id", "name", "status", "userId", "created_at"],
      order: [["created_at", "DESC"]],
      limit: 5,
    });

    res.json({
      success: true,
      data: {
        userStats: {
          total: totalUsers,
          active: activeUsers,
          admin: adminUsers,
        },
        contentStats: {
          total: totalContents,
          published: publishedContents,
        },
        recentUsers,
        recentContents,
      },
    });
  } catch (error) {
    console.error("获取系统概览数据失败:", error);
    res.status(500).json({
      success: false,
      error: `获取系统概览数据失败: ${error.message}`,
    });
  }
}

module.exports = {
  getUsers,
  getUserDetail,
  updateUserStatus,
  updateUserRole,
  getAdminLogs,
  getDashboardStats,
};
