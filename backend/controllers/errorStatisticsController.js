const errorStatisticsService = require("../services/errorStatisticsService");

/**
 * 上报错误
 */
async function reportError(req, res) {
  try {
    const errorData = {
      ...req.body,
      ip_address: req.ip,
      user_id: req.user?.id || null,
      session_id: req.sessionID || null,
    };

    // 验证必需字段
    const requiredFields = ["error_type", "message", "url"];
    for (const field of requiredFields) {
      if (!errorData[field]) {
        return res.status(400).json({
          success: false,
          error: `缺少必需字段: ${field}`,
        });
      }
    }

    const result = await errorStatisticsService.recordError(errorData);

    if (result.success) {
      res.json({
        success: true,
        data: { error_hash: result.error_hash },
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error) {
    console.error("上报错误失败:", error);
    res.status(500).json({
      success: false,
      error: "服务器内部错误",
    });
  }
}

/**
 * 批量上报错误
 */
async function reportErrors(req, res) {
  try {
    const { errors } = req.body;

    if (!Array.isArray(errors) || errors.length === 0) {
      return res.status(400).json({
        success: false,
        error: "错误列表不能为空",
      });
    }

    // 为每个错误添加请求信息
    const errorList = errors.map((errorData) => ({
      ...errorData,
      ip_address: req.ip,
      user_id: req.user?.id || null,
      session_id: req.sessionID || null,
    }));

    const result = await errorStatisticsService.recordErrors(errorList);

    res.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("批量上报错误失败:", error);
    res.status(500).json({
      success: false,
      error: "服务器内部错误",
    });
  }
}

/**
 * 获取错误汇总列表
 */
async function getErrorSummaries(req, res) {
  try {
    const options = {
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 20,
      error_type: req.query.error_type,
      status: req.query.status,
      priority: req.query.priority,
      severity_level: req.query.severity_level,
      start_date: req.query.start_date,
      end_date: req.query.end_date,
      order_by: req.query.order_by || "last_seen",
      order_direction: req.query.order_direction || "DESC",
    };

    const result = await errorStatisticsService.getErrorSummaries(options);

    res.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("获取错误汇总失败:", error);
    res.status(500).json({
      success: false,
      error: "服务器内部错误",
    });
  }
}

/**
 * 获取错误详情
 */
async function getErrorDetails(req, res) {
  try {
    const { errorHash } = req.params;

    const result = await errorStatisticsService.getErrorDetails(errorHash);

    res.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("获取错误详情失败:", error);

    if (error.message === "错误不存在") {
      res.status(404).json({
        success: false,
        error: error.message,
      });
    } else {
      res.status(500).json({
        success: false,
        error: "服务器内部错误",
      });
    }
  }
}

/**
 * 更新错误状态
 */
async function updateErrorStatus(req, res) {
  try {
    const { errorHash } = req.params;
    const { status } = req.body;
    const userId = req.user?.id;

    if (!status) {
      return res.status(400).json({
        success: false,
        error: "状态不能为空",
      });
    }

    const validStatuses = ["new", "acknowledged", "resolved", "ignored"];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        error: "无效的状态值",
      });
    }

    const result = await errorStatisticsService.updateErrorStatus(
      errorHash,
      status,
      userId
    );

    res.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("更新错误状态失败:", error);

    if (error.message === "错误不存在") {
      res.status(404).json({
        success: false,
        error: error.message,
      });
    } else {
      res.status(500).json({
        success: false,
        error: "服务器内部错误",
      });
    }
  }
}

/**
 * 获取错误概览数据
 */
async function getOverview(req, res) {
  try {
    const { ErrorSummary, sequelize } = require("../models");

    // 获取总体统计
    const totalErrors = await ErrorSummary.count();
    const newErrors = await ErrorSummary.count({ where: { status: "new" } });
    const resolvedErrors = await ErrorSummary.count({
      where: { status: "resolved" },
    });

    // 获取按类型分组的统计
    const errorsByType = await ErrorSummary.findAll({
      attributes: [
        "error_type",
        [sequelize.fn("COUNT", sequelize.col("id")), "count"],
        [
          sequelize.fn("SUM", sequelize.col("occurrence_count")),
          "total_occurrences",
        ],
      ],
      group: ["error_type"],
    });

    // 获取按状态分组的统计
    const errorsByStatus = await ErrorSummary.findAll({
      attributes: [
        "status",
        [sequelize.fn("COUNT", sequelize.col("id")), "count"],
      ],
      group: ["status"],
    });

    res.json({
      success: true,
      data: {
        overview: {
          total_errors: totalErrors,
          new_errors: newErrors,
          resolved_errors: resolvedErrors,
        },
        by_type: errorsByType,
        by_status: errorsByStatus,
      },
    });
  } catch (error) {
    console.error("获取错误概览失败:", error);
    res.status(500).json({
      success: false,
      error: "服务器内部错误",
    });
  }
}

/**
 * 获取错误统计数据
 */
async function getStatistics(req, res) {
  try {
    const options = {
      start_date: req.query.start_date,
      end_date: req.query.end_date,
      error_type: req.query.error_type,
      group_by: req.query.group_by || "date",
    };

    const result = await errorStatisticsService.getStatistics(options);

    res.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("获取错误统计失败:", error);
    res.status(500).json({
      success: false,
      error: "服务器内部错误",
    });
  }
}

/**
 * 更新错误优先级
 */
async function updateErrorPriority(req, res) {
  try {
    const { errorHash } = req.params;
    const { priority } = req.body;

    if (!priority) {
      return res.status(400).json({
        success: false,
        error: "优先级不能为空",
      });
    }

    const validPriorities = ["low", "medium", "high", "critical"];
    if (!validPriorities.includes(priority)) {
      return res.status(400).json({
        success: false,
        error: "无效的优先级值",
      });
    }

    const { ErrorSummary } = require("../models");
    const summary = await ErrorSummary.findOne({
      where: { error_hash: errorHash },
    });

    if (!summary) {
      return res.status(404).json({
        success: false,
        error: "错误不存在",
      });
    }

    await summary.update({ priority });

    res.json({
      success: true,
      data: { success: true },
    });
  } catch (error) {
    console.error("更新错误优先级失败:", error);
    res.status(500).json({
      success: false,
      error: "服务器内部错误",
    });
  }
}

/**
 * 添加处理备注
 */
async function addResolutionNotes(req, res) {
  try {
    const { errorHash } = req.params;
    const { notes } = req.body;

    if (!notes) {
      return res.status(400).json({
        success: false,
        error: "备注内容不能为空",
      });
    }

    const { ErrorSummary } = require("../models");
    const summary = await ErrorSummary.findOne({
      where: { error_hash: errorHash },
    });

    if (!summary) {
      return res.status(404).json({
        success: false,
        error: "错误不存在",
      });
    }

    await summary.update({ resolution_notes: notes });

    res.json({
      success: true,
      data: { success: true },
    });
  } catch (error) {
    console.error("添加处理备注失败:", error);
    res.status(500).json({
      success: false,
      error: "服务器内部错误",
    });
  }
}

module.exports = {
  reportError,
  reportErrors,
  getErrorSummaries,
  getErrorDetails,
  updateErrorStatus,
  updateErrorPriority,
  addResolutionNotes,
  getOverview,
  getStatistics,
};
