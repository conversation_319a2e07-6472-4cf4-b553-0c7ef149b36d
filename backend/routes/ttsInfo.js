/**
 * TTS服务信息路由
 * 提供TTS服务和声音ID的相关信息
 */
const express = require("express");
const router = express.Router();
const db = require("../models");

// 服务信息
const services = [
  {
    id: "google",
    name: "Google TTS",
    description: "Google Cloud Text-to-Speech服务",
  },
  {
    id: "baidu",
    name: "百度TTS",
    description: "百度语音合成服务",
  },
];

// 语言信息
const languages = {
  ja: {
    name: "日语",
  },
  "zh-CN": {
    name: "简体中文",
  },
  "zh-TW": {
    name: "繁体中文",
  },

  en: {
    name: "英语",
  },
};

// 动态获取默认映射关系
async function getDefaultMappings() {
  try {
    // 获取所有声音信息
    const voices = await db.Voice.findAll({
      where: { disabled: false },
      attributes: ["id", "speaker_id", "language_code"],
    });

    // 创建speaker_id到数据库ID的映射
    const speakerIdToDbId = {};
    voices.forEach((voice) => {
      speakerIdToDbId[voice.speaker_id] = voice.id;
    });

    // 构建默认映射 - 使用speaker_id查找对应的数据库ID
    const mappings = {
      ja: {
        default: speakerIdToDbId["ja-JP-Wavenet-A"] || null,
        A: speakerIdToDbId["ja-JP-Wavenet-A"] || null,
        B: speakerIdToDbId["ja-JP-Wavenet-C"] || null,
        C: speakerIdToDbId["ja-JP-Wavenet-D"] || null,
      },
      "zh-CN": {
        default: speakerIdToDbId["cmn-CN-Wavenet-A"] || null,
        A: speakerIdToDbId["cmn-CN-Wavenet-A"] || null,
        B: speakerIdToDbId["cmn-CN-Wavenet-B"] || null,
        C: speakerIdToDbId["cmn-CN-Wavenet-D"] || null,
      },
      "zh-TW": {
        default: speakerIdToDbId["cmn-TW-Wavenet-A"] || null,
        A: speakerIdToDbId["cmn-TW-Wavenet-A"] || null,
        B: speakerIdToDbId["cmn-TW-Wavenet-B"] || null,
        C: speakerIdToDbId["cmn-TW-Wavenet-C"] || null,
      },
      en: {
        default: speakerIdToDbId["en-US-Wavenet-A"] || null,
        A: speakerIdToDbId["en-US-Wavenet-A"] || null,
        B: speakerIdToDbId["en-US-Wavenet-B"] || null,
        C: speakerIdToDbId["en-US-Wavenet-C"] || null,
      },
    };

    // 过滤掉null值
    Object.keys(mappings).forEach((lang) => {
      Object.keys(mappings[lang]).forEach((speaker) => {
        if (mappings[lang][speaker] === null) {
          delete mappings[lang][speaker];
        }
      });
    });

    return mappings;
  } catch (error) {
    console.error("获取默认映射失败:", error);
    return {};
  }
}

/**
 * 获取TTS服务信息
 * GET /api/tts/info
 */
router.get("/info", async (_, res) => {
  try {
    // 从数据库获取声音信息
    const voices = await db.Voice.findAll({
      where: {
        disabled: false,
      },
    });

    // 按语言分组声音ID
    const voicesByLanguage = {};
    voices.forEach((voice) => {
      const langCode = voice.language_code;
      if (!voicesByLanguage[langCode]) {
        voicesByLanguage[langCode] = [];
      }
      voicesByLanguage[langCode].push(voice.id);
    });

    // 构建语言信息
    const languagesInfo = {};
    Object.entries(languages).forEach(([code, info]) => {
      languagesInfo[code] = {
        name: info.name,
        voice_ids: voicesByLanguage[code] || [],
      };
    });

    // 构建TTS服务信息
    const ttsServices = {
      // 声音信息表 - 只在顶层出现一次，直接包含服务名称
      voices: voices.map((voice) => {
        // 查找对应的服务信息
        const service = services.find((s) => s.id === voice.service_id) || {
          name: "未知服务",
        };

        return {
          id: voice.id,
          speaker_id: voice.speaker_id,
          service_id: voice.service_id,
          service_name: service.name, // 直接添加服务名称
          language_code: voice.language_code,
          name: voice.name,
          gender: voice.gender,
          is_premium: voice.is_premium,
          disabled: voice.disabled,
          api_params: voice.api_params,
        };
      }),

      // 语言信息 - 只包含名称和声音ID引用
      languages: languagesInfo,

      // 默认映射关系
      defaultMappings: await getDefaultMappings(),
    };

    res.json({
      success: true,
      data: ttsServices,
    });
  } catch (error) {
    console.error("获取TTS服务信息失败:", error);
    res.status(500).json({
      success: false,
      error: "获取TTS服务信息失败: " + error.message,
    });
  }
});

/**
 * 获取声音信息
 * GET /api/tts/voices/:voiceId
 */
router.get("/voices/:voiceId", async (req, res) => {
  try {
    const { voiceId } = req.params;

    // 从数据库获取声音信息
    const voice = await db.Voice.findByPk(voiceId);

    if (!voice) {
      return res.status(404).json({
        success: false,
        error: `未找到声音ID: ${voiceId}`,
      });
    }

    res.json({
      success: true,
      data: voice,
    });
  } catch (error) {
    console.error(`获取声音信息失败: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `获取声音信息失败: ${error.message}`,
    });
  }
});

// 导出路由和声音信息表
module.exports = {
  router,
  getDefaultMappings,
};
