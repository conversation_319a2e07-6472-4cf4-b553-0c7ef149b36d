/**
 * 公开内容API路由
 * 提供公开内容的获取和搜索功能
 */
const express = require("express");
const router = express.Router();
const db = require("../models");
const { Op } = require("sequelize");

/**
 * 获取所有公开内容
 * GET /api/public/contents
 */
router.get("/", async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 10;
    const sortBy = req.query.sortBy || "updatedAt";
    const sortOrder = req.query.sortOrder || "DESC";
    const category = req.query.category; // 分类过滤



    // 构建查询条件
    const where = {
      status: "published",
    };

    // 添加分类过滤
    if (category) {
      where.tags = {
        [Op.like]: `%${category}%`,
      };
    }

    // 添加标签过滤（多选）
    if (req.query.tags) {
      const tags = req.query.tags.split(',');
      where.tags = {
        [Op.or]: tags.map(tag => ({
          [Op.like]: `%${tag.trim()}%`
        }))
      };
    }



    const { count, rows } = await db.Content.findAndCountAll({
      where,
      attributes: [
        "id",
        "name",
        "description",
        ["thumbnail_url", "thumbnailUrl"],
        "tags",
        "status",
        ["created_at", "createdAt"],
        ["updated_at", "updatedAt"],
        [
          db.sequelize.literal(
            `(SELECT COUNT(*) FROM content_views WHERE content_views.content_id = Content.id)`
          ),
          "viewCount"
        ],
      ],
      include: [
        {
          model: db.User,
          as: "creator",
          attributes: ["id", "username"],
        },
      ],
      order: [[sortBy, sortOrder]],
      limit: pageSize,
      offset: (page - 1) * pageSize,
    });

    // 获取所有标签用于过滤选项
    const allTags = await db.Content.findAll({
      where: { status: "published" },
      attributes: ["tags"],
      raw: true,
    });

    const tagSet = new Set();
    allTags.forEach(item => {
      if (item.tags) {
        item.tags.split(',').forEach(tag => {
          const trimmedTag = tag.trim();
          if (trimmedTag) tagSet.add(trimmedTag);
        });
      }
    });

    res.json({
      success: true,
      contents: rows,
      pagination: {
        total: count,
        page,
        pageSize,
      },
      availableTags: Array.from(tagSet).sort(),
    });
  } catch (error) {
    console.error("获取公开内容列表失败:", error);
    res.status(500).json({
      success: false,
      error: `获取公开内容列表失败: ${error.message}`,
    });
  }
});

/**
 * 获取热门内容
 * GET /api/public/contents/hot
 */
router.get("/hot", async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 6;

    // 这里可以根据实际需求定义热门内容的排序规则
    // 例如，可以根据访问量、点赞数等排序
    // 这里暂时使用更新时间排序
    const contents = await db.Content.findAll({
      where: {
        status: "published",
      },
      attributes: [
        "id",
        "name",
        "description",
        ["thumbnail_url", "thumbnailUrl"],
        "tags",
        "status",
        ["created_at", "createdAt"],
        ["updated_at", "updatedAt"],
        [
          db.sequelize.literal(
            `(SELECT COUNT(*) FROM content_views WHERE content_views.content_id = Content.id)`
          ),
          "viewCount"
        ],
      ],
      include: [
        {
          model: db.User,
          as: "creator",
          attributes: ["id", "username", "email", "created_at"],
        },
      ],
      order: [["updated_at", "DESC"]],
      limit,
    });

    res.json({
      success: true,
      contents,
    });
  } catch (error) {
    console.error("获取热门内容失败:", error);
    res.status(500).json({
      success: false,
      error: `获取热门内容失败: ${error.message}`,
    });
  }
});

/**
 * 获取最新内容
 * GET /api/public/contents/latest
 */
router.get("/latest", async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 6;

    const contents = await db.Content.findAll({
      where: {
        status: "published",
      },
      attributes: [
        "id",
        "name",
        "description",
        ["thumbnail_url", "thumbnailUrl"],
        "tags",
        "status",
        ["created_at", "createdAt"],
        ["updated_at", "updatedAt"],
        [
          db.sequelize.literal(
            `(SELECT COUNT(*) FROM content_views WHERE content_views.content_id = Content.id)`
          ),
          "viewCount"
        ],
      ],
      include: [
        {
          model: db.User,
          as: "creator",
          attributes: ["id", "username", "email", "created_at"],
        },
      ],
      order: [["created_at", "DESC"]],
      limit,
    });

    res.json({
      success: true,
      contents,
    });
  } catch (error) {
    console.error("获取最新内容失败:", error);
    res.status(500).json({
      success: false,
      error: `获取最新内容失败: ${error.message}`,
    });
  }
});

/**
 * 搜索内容
 * GET /api/public/contents/search
 */
router.get("/search", async (req, res) => {
  try {
    const keyword = req.query.keyword || "";
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 10;

    if (!keyword.trim()) {
      return res.json({
        success: true,
        contents: [],
        pagination: {
          total: 0,
          page,
          pageSize,
        },
      });
    }

    const where = {
      status: "published",
      [Op.or]: [
        {
          name: {
            [Op.like]: `%${keyword}%`,
          },
        },
        {
          description: {
            [Op.like]: `%${keyword}%`,
          },
        },
        {
          tags: {
            [Op.like]: `%${keyword}%`,
          },
        },
      ],
    };

    const { count, rows } = await db.Content.findAndCountAll({
      where,
      attributes: [
        "id",
        "name",
        "description",
        ["thumbnail_url", "thumbnailUrl"],
        "tags",
        "status",
        ["created_at", "createdAt"],
        ["updated_at", "updatedAt"],
        [
          db.sequelize.literal(
            `(SELECT COUNT(*) FROM content_views WHERE content_views.content_id = Content.id)`
          ),
          "viewCount"
        ],
      ],
      include: [
        {
          model: db.User,
          as: "creator",
          attributes: ["id", "username"],
        },
      ],
      order: [["updated_at", "DESC"]],
      limit: pageSize,
      offset: (page - 1) * pageSize,
    });

    res.json({
      success: true,
      contents: rows,
      pagination: {
        total: count,
        page,
        pageSize,
      },
    });
  } catch (error) {
    console.error("搜索内容失败:", error);
    res.status(500).json({
      success: false,
      error: `搜索内容失败: ${error.message}`,
    });
  }
});

/**
 * 按标签获取内容
 * GET /api/public/contents/tag/:tag
 */
router.get("/tag/:tag", async (req, res) => {
  try {
    const tag = req.params.tag;
    const limit = parseInt(req.query.limit) || 20;

    if (!tag) {
      return res.status(400).json({
        success: false,
        error: "缺少标签参数",
      });
    }

    const contents = await db.Content.findAll({
      where: {
        status: "published",
        tags: {
          [Op.like]: `%${tag}%`,
        },
      },
      attributes: [
        "id",
        "name",
        "description",
        ["thumbnail_url", "thumbnailUrl"],
        "tags",
        "status",
        ["created_at", "createdAt"],
        ["updated_at", "updatedAt"],
        [
          db.sequelize.literal(
            `(SELECT COUNT(*) FROM content_views WHERE content_views.content_id = Content.id)`
          ),
          "viewCount"
        ],
      ],
      include: [
        {
          model: db.User,
          as: "creator",
          attributes: ["id", "username"],
        },
      ],
      order: [["updated_at", "DESC"]],
      limit,
    });

    res.json({
      success: true,
      contents,
    });
  } catch (error) {
    console.error(`获取标签内容失败 (标签: ${req.params.tag}):`, error);
    res.status(500).json({
      success: false,
      error: `获取标签内容失败: ${error.message}`,
    });
  }
});

/**
 * 获取单个公开内容
 * GET /api/public/contents/:id
 */
router.get("/:id", async (req, res) => {
  try {
    const content = await db.Content.findOne({
      where: {
        id: req.params.id,
        status: "published",
      },
    });

    if (!content) {
      return res.status(404).json({
        success: false,
        error: "内容不存在或未发布",
      });
    }

    res.json({
      success: true,
      content,
    });
  } catch (error) {
    console.error(`获取公开内容失败 (ID: ${req.params.id}):`, error);
    res.status(500).json({
      success: false,
      error: `获取公开内容失败: ${error.message}`,
    });
  }
});

module.exports = router;
