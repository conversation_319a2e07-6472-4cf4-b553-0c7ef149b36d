/**
 * 管理员路由索引
 * 注册所有管理员相关的子路由
 */
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middleware/authMiddleware');
const { adminOnly } = require('../../middleware/adminMiddleware');

// 用户等级管理路由
router.use('/user-levels', require('./userLevels'));

// 订阅管理路由
router.use('/subscriptions', require('./subscriptions'));

// 权限管理路由
router.use('/permissions', require('./permissions'));

// 搜索用户
router.get('/users/search', authenticate, adminOnly, async (req, res) => {
  try {
    const { query } = req.query;
    
    if (!query || query.length < 2) {
      return res.json({
        success: true,
        users: []
      });
    }
    
    const db = req.app.get('db');
    const { Op } = db.Sequelize;
    
    // 搜索用户
    const users = await db.User.findAll({
      where: {
        [Op.or]: [
          { id: query },
          { email: { [Op.like]: `%${query}%` } },
          { username: { [Op.like]: `%${query}%` } }
        ]
      },
      attributes: ['id', 'email', 'username', 'level'],
      limit: 10
    });
    
    res.json({
      success: true,
      users
    });
  } catch (error) {
    console.error('搜索用户失败:', error);
    res.status(500).json({
      success: false,
      error: '搜索用户失败'
    });
  }
});

module.exports = router;
