/**
 * 功能权限API路由
 * 提供功能权限的管理功能
 */
const express = require("express");
const router = express.Router();
const db = require("../models");
const { authenticate } = require("../middleware/authMiddleware");
const { adminOnly } = require("../middleware/adminMiddleware");

/**
 * 获取所有功能特性
 * GET /api/feature-permissions/features
 */
router.get("/features", async (req, res) => {
  try {
    // 从数据库获取所有功能特性
    const featureFlags = await db.FeatureFlag.findAll({
      attributes: [
        "id",
        "featureKey",
        "displayName",
        "description",
        "isEnabled",
      ],
      order: [["featureKey", "ASC"]],
    });

    // 将功能特性转换为前端需要的格式
    const features = featureFlags.map((flag) => {
      // 使用数据库中的显示名称，如果没有则生成一个
      let name = flag.displayName;

      // 如果数据库中没有显示名称，则根据功能键名生成一个
      if (!name) {
        // 将下划线分隔的键名转换为空格分隔的名称
        name = flag.featureKey.replace(/_/g, " ");
        // 首字母大写
        name = name.charAt(0).toUpperCase() + name.slice(1);
      }

      return {
        id: flag.id,
        key: flag.featureKey,
        name: name,
        description: flag.description || `访问${name}功能`,
        isEnabled: flag.isEnabled,
      };
    });

    res.json({
      success: true,
      features,
    });
  } catch (error) {
    console.error("获取功能特性失败:", error);
    res.status(500).json({
      success: false,
      error: "获取功能特性失败",
    });
  }
});

/**
 * 获取等级功能映射
 * GET /api/feature-permissions/level-mapping
 */
router.get("/level-mapping", async (req, res) => {
  try {
    // 从数据库获取所有等级权限
    const levelPermissions = await db.LevelPermission.findAll({
      attributes: ["level", "featureKey"],
    });

    // 将等级权限转换为前端需要的格式
    const levelFeatures = {};

    // 处理每个等级权限
    levelPermissions.forEach((permission) => {
      const level = permission.level;
      const featureKey = permission.featureKey;

      // 如果等级不存在，创建一个空数组
      if (!levelFeatures[level]) {
        levelFeatures[level] = [];
      }

      // 添加功能到等级
      if (!levelFeatures[level].includes(featureKey)) {
        levelFeatures[level].push(featureKey);
      }
    });

    // 确保至少有等级0
    if (!levelFeatures[0]) {
      levelFeatures[0] = [];
    }

    res.json({
      success: true,
      levelFeatures,
    });
  } catch (error) {
    console.error("获取等级功能映射失败:", error);
    res.status(500).json({
      success: false,
      error: "获取等级功能映射失败",
    });
  }
});

/**
 * 检查当前用户的功能权限
 * GET /api/feature-permissions/check/:featureKey
 */
router.get("/check/:featureKey", authenticate, async (req, res) => {
  try {
    const featureKey = req.params.featureKey;

    // 管理员始终有权限
    if (req.user.role === "admin") {
      return res.json({
        success: true,
        hasPermission: true,
      });
    }

    // 检查功能是否全局启用
    const featureFlag = await db.FeatureFlag.findOne({
      where: { featureKey },
    });

    if (featureFlag && featureFlag.isEnabled) {
      // 功能全局启用，所有用户都可以访问
      return res.json({
        success: true,
        hasPermission: true,
      });
    }

    // 检查用户是否有特定权限
    const permission = await db.FeaturePermission.findOne({
      where: {
        featureKey,
        userId: req.user.id,
      },
    });

    res.json({
      success: true,
      hasPermission: !!permission,
    });
  } catch (error) {
    console.error("检查功能权限失败:", error);
    res.status(500).json({
      success: false,
      error: `检查功能权限失败: ${error.message}`,
    });
  }
});

/**
 * 创建新功能权限
 * POST /api/feature-permissions/features
 */
router.post("/features", authenticate, adminOnly, async (req, res) => {
  try {
    const { featureKey, displayName, description, isEnabled } = req.body;

    if (!featureKey) {
      return res.status(400).json({
        success: false,
        error: "功能标识符不能为空",
      });
    }

    // 检查功能标识符是否已存在
    const existingFeature = await db.FeatureFlag.findOne({
      where: { featureKey },
    });

    if (existingFeature) {
      return res.status(400).json({
        success: false,
        error: "功能标识符已存在",
      });
    }

    // 创建新功能权限
    const feature = await db.FeatureFlag.create({
      featureKey,
      displayName: displayName || null,
      description: description || null,
      isEnabled: isEnabled !== undefined ? isEnabled : false,
    });

    res.status(201).json({
      success: true,
      feature,
    });
  } catch (error) {
    console.error("创建功能权限失败:", error);
    res.status(500).json({
      success: false,
      error: `创建功能权限失败: ${error.message}`,
    });
  }
});

module.exports = router;
