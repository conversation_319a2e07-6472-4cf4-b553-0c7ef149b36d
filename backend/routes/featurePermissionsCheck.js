/**
 * 功能权限检查API路由
 * 提供检查用户是否有权限访问特定功能的API
 */
const express = require('express');
const router = express.Router();
const db = require('../models');
const { authenticate } = require('../middleware/authMiddleware');

/**
 * 检查当前用户的功能权限
 * GET /api/feature-permissions/check/:featureKey
 */
router.get('/:featureKey', authenticate, async (req, res) => {
  try {
    const featureKey = req.params.featureKey;
    
    // 管理员始终有权限
    if (req.user.role === 'admin') {
      return res.json({
        success: true,
        hasPermission: true
      });
    }

    // 检查功能是否全局启用
    const featureFlag = await db.FeatureFlag.findOne({
      where: { featureKey }
    });

    if (featureFlag && featureFlag.isEnabled) {
      // 功能全局启用，所有用户都可以访问
      return res.json({
        success: true,
        hasPermission: true
      });
    }

    // 检查用户是否有特定权限
    const permission = await db.FeaturePermission.findOne({
      where: {
        featureKey,
        userId: req.user.id
      }
    });

    res.json({
      success: true,
      hasPermission: !!permission
    });
  } catch (error) {
    console.error('检查功能权限失败:', error);
    res.status(500).json({
      success: false,
      error: `检查功能权限失败: ${error.message}`
    });
  }
});

module.exports = router;
