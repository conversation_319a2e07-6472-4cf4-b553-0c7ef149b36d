const express = require("express");
const router = express.Router();
const axios = require("axios");

// DeepSeek API 配置
const DEEPSEEK_API_KEY = "***********************************";
const DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions";

// 语言代码到语言名称的映射
const LANGUAGE_NAMES = {
  "zh-CN": "简体中文",
  "zh-TW": "繁体中文",
  en: "英语",
  ja: "日语",
};

// 验证DeepSeek API密钥
if (!DEEPSEEK_API_KEY) {
  console.warn("警告: DeepSeek API密钥未设置，翻译功能将不可用");
}

/**
 * 调用DeepSeek API进行翻译
 * @param {Array} texts 文本数组，每个元素包含 {id, content, language}
 * @param {Array} targetLanguages 目标语言数组
 * @returns {Promise<Object>} 翻译结果，格式为 {textId: {lang: translation}}
 */
async function callDeepSeekTranslate(texts, targetLanguages) {
  if (!DEEPSEEK_API_KEY) {
    throw new Error("DeepSeek API密钥未配置");
  }

  if (texts.length === 0 || targetLanguages.length === 0) {
    return {};
  }

  // 按源语言分组文本
  const textsBySourceLang = {};
  texts.forEach((text) => {
    const sourceLang = text.language;
    if (!textsBySourceLang[sourceLang]) {
      textsBySourceLang[sourceLang] = [];
    }
    textsBySourceLang[sourceLang].push(text);
  });

  const allResults = {};

  // 为每种源语言分别处理
  for (const [sourceLang, sourceTexts] of Object.entries(textsBySourceLang)) {
    // 过滤掉与源语言相同的目标语言
    const validTargetLanguages = targetLanguages.filter(
      (targetLang) => targetLang !== sourceLang
    );

    if (validTargetLanguages.length === 0) {
      // 如果没有有效的目标语言，跳过
      continue;
    }

    // 构建翻译prompt
    const sourceLanguageName = LANGUAGE_NAMES[sourceLang] || sourceLang;
    const targetLanguageNames = validTargetLanguages
      .map((lang) => LANGUAGE_NAMES[lang] || lang)
      .join("、");

    // 构建文本列表
    const textList = sourceTexts
      .map((text) => `[${text.id}] ${text.content}`)
      .join("\n");

    const prompt = `请将以下${sourceLanguageName}文本翻译为${targetLanguageNames}，保持段落对应关系：

${textList}

重要：请严格按以下JSON格式返回，不要使用Markdown代码块格式（不要用\`\`\`json或\`\`\`），不要添加任何解释文字，只返回纯JSON：
{
${sourceTexts
  .map((text) => {
    const translations = validTargetLanguages
      .map((lang) => `    "${lang}": "翻译内容"`)
      .join(",\n");
    return `  "${text.id}": {\n${translations}\n  }`;
  })
  .join(",\n")}
}`;

    try {
      console.log(
        `使用DeepSeek翻译: ${sourceLang} -> [${validTargetLanguages.join(
          ", "
        )}], ${sourceTexts.length} 个文本`
      );

      const response = await axios.post(
        DEEPSEEK_API_URL,
        {
          model: "deepseek-chat",
          messages: [
            {
              role: "user",
              content: prompt,
            },
          ],
          max_tokens: 8000,
          temperature: 0.1,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${DEEPSEEK_API_KEY}`,
          },
          timeout: 600000, // 10分钟超时
        }
      );

      if (response.data?.choices?.[0]?.message?.content) {
        let content = response.data.choices[0].message.content.trim();

        try {
          // 清理DeepSeek返回的内容，移除可能的Markdown代码块标记和其他格式
          content = content.replace(/^```json\s*/i, "").replace(/\s*```$/i, "");
          content = content.replace(/^```\s*/i, "").replace(/\s*```$/i, "");
          content = content.replace(/^json\s*/i, ""); // 移除可能的 "json" 前缀
          content = content.replace(/^\s*{\s*$[\s\S]*^\s*}\s*$/m, content); // 确保只保留JSON部分
          content = content.trim();

          // 如果内容不是以 { 开头，尝试找到第一个 {
          if (!content.startsWith("{")) {
            const jsonStart = content.indexOf("{");
            if (jsonStart !== -1) {
              content = content.substring(jsonStart);
            }
          }

          // 如果内容不是以 } 结尾，尝试找到最后一个 }
          if (!content.endsWith("}")) {
            const jsonEnd = content.lastIndexOf("}");
            if (jsonEnd !== -1) {
              content = content.substring(0, jsonEnd + 1);
            }
          }

          console.log(
            "清理后的DeepSeek响应内容:",
            content.substring(0, 200) + (content.length > 200 ? "..." : "")
          );

          // 尝试解析JSON响应
          const translationResult = JSON.parse(content);

          // 合并到总结果中
          Object.assign(allResults, translationResult);
        } catch (parseError) {
          console.error("DeepSeek返回的JSON格式无效:", content);
          console.error("解析错误:", parseError.message);

          // 解析失败时，为这批文本返回空翻译
          sourceTexts.forEach((text) => {
            allResults[text.id] = {};
            validTargetLanguages.forEach((lang) => {
              allResults[text.id][lang] = "";
            });
          });
        }
      } else {
        console.error("DeepSeek API返回格式异常:", response.data);
        throw new Error("DeepSeek API返回格式异常");
      }
    } catch (error) {
      console.error(`DeepSeek翻译请求失败 (${sourceLang}):`, error.message);

      // 请求失败时，为这批文本返回空翻译
      sourceTexts.forEach((text) => {
        allResults[text.id] = {};
        validTargetLanguages.forEach((lang) => {
          allResults[text.id][lang] = "";
        });
      });
    }
  }

  return allResults;
}

// DeepSeek翻译API
router.post("/", async (req, res) => {
  try {
    const { targetLanguages, texts } = req.body;

    console.log("收到DeepSeek翻译请求:");
    console.log("目标语言:", targetLanguages);
    console.log("文本数量:", texts?.length || 0);

    // 验证请求参数
    if (
      !targetLanguages ||
      !Array.isArray(targetLanguages) ||
      targetLanguages.length === 0
    ) {
      return res.status(400).json({ error: "缺少目标语言参数或格式不正确" });
    }

    if (!texts || !Array.isArray(texts) || texts.length === 0) {
      return res.status(400).json({ error: "缺少文本参数或格式不正确" });
    }

    // 检查文本格式
    const hasInvalidText = texts.some(
      (text) => !text.id || !text.content || !text.language
    );
    if (hasInvalidText) {
      return res.status(400).json({
        error: "文本格式不正确，每个文本必须包含 id、content 和 language",
      });
    }

    // 调用DeepSeek翻译服务
    const deepSeekResults = await callDeepSeekTranslate(texts, targetLanguages);

    // 将DeepSeek结果转换为前端期望的格式
    const translationResults = texts.map((text) => ({
      id: text.id,
      content: text.content,
      translations: deepSeekResults[text.id] || {},
    }));

    // 返回翻译结果
    console.log(`DeepSeek翻译完成，返回 ${translationResults.length} 个结果`);

    return res.json({
      success: true,
      translations: translationResults,
    });
  } catch (error) {
    console.error("DeepSeek翻译请求处理失败:", error);
    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

module.exports = router;
