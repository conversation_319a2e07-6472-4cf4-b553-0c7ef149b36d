const express = require("express");
const router = express.Router();
const errorStatisticsController = require("../controllers/errorStatisticsController");
const { authenticate } = require("../middleware/authMiddleware");
const { adminOnly } = require("../middleware/adminMiddleware");

// 错误上报接口（无需认证，允许匿名上报）
router.post("/report", errorStatisticsController.reportError);
router.post("/report/batch", errorStatisticsController.reportErrors);

// 错误管理接口（需要管理员权限）
router.get(
  "/summaries",
  authenticate,
  adminOnly,
  errorStatisticsController.getErrorSummaries
);
router.get(
  "/overview",
  authenticate,
  adminOnly,
  errorStatisticsController.getOverview
);
router.get(
  "/statistics",
  authenticate,
  adminOnly,
  errorStatisticsController.getStatistics
);
router.get(
  "/:errorHash/details",
  authenticate,
  adminOnly,
  errorStatisticsController.getErrorDetails
);

// 错误状态管理接口（需要管理员权限）
router.put(
  "/:errorHash/status",
  authenticate,
  adminOnly,
  errorStatisticsController.updateErrorStatus
);
router.put(
  "/:errorHash/priority",
  authenticate,
  adminOnly,
  errorStatisticsController.updateErrorPriority
);
router.post(
  "/:errorHash/notes",
  authenticate,
  adminOnly,
  errorStatisticsController.addResolutionNotes
);

module.exports = router;
