'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('collection_items', {
      id: {
        type: Sequelize.STRING(21),
        primaryKey: true,
        allowNull: false
      },
      collection_id: {
        type: Sequelize.STRING(21),
        allowNull: false,
        comment: '合集ID'
      },
      content_id: {
        type: Sequelize.STRING(21),
        allowNull: false,
        comment: '内容ID'
      },
      sort_order: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: '排序顺序'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 添加外键约束
    await queryInterface.addConstraint('collection_items', {
      fields: ['collection_id'],
      type: 'foreign key',
      name: 'fk_collection_items_collection_id',
      references: {
        table: 'collections',
        field: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    });

    await queryInterface.addConstraint('collection_items', {
      fields: ['content_id'],
      type: 'foreign key',
      name: 'fk_collection_items_content_id',
      references: {
        table: 'contents',
        field: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    });

    // 添加索引
    await queryInterface.addIndex('collection_items', ['collection_id']);
    await queryInterface.addIndex('collection_items', ['content_id']);
    await queryInterface.addIndex('collection_items', ['sort_order']);
    
    // 添加唯一约束
    await queryInterface.addConstraint('collection_items', {
      fields: ['collection_id', 'content_id'],
      type: 'unique',
      name: 'unique_collection_content'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('collection_items');
  }
};
