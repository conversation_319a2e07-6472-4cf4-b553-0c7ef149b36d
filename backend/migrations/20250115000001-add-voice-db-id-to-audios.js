'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 添加 voice_db_id 字段到 audios 表
    await queryInterface.addColumn('audios', 'voice_db_id', {
      type: Sequelize.INTEGER,
      allowNull: true, // 暂时可空，用于兼容现有数据
      references: {
        model: 'voices',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
      comment: '关联voices表的ID，用于标识具体的声音配置'
    });

    // 添加索引以优化查询性能
    await queryInterface.addIndex('audios', ['voice_db_id'], {
      name: 'idx_voice_db_id'
    });

    // 修改 speaker 字段为可空（向后兼容）
    await queryInterface.changeColumn('audios', 'speaker', {
      type: Sequelize.STRING(50),
      allowNull: true, // 改为可空，因为新记录应该使用voice_db_id
    });

    console.log('✅ 已添加 voice_db_id 字段到 audios 表');
  },

  async down(queryInterface, Sequelize) {
    // 删除索引
    await queryInterface.removeIndex('audios', 'idx_voice_db_id');
    
    // 删除 voice_db_id 字段
    await queryInterface.removeColumn('audios', 'voice_db_id');

    // 恢复 speaker 字段为非空
    await queryInterface.changeColumn('audios', 'speaker', {
      type: Sequelize.STRING(50),
      allowNull: false,
    });

    console.log('✅ 已从 audios 表删除 voice_db_id 字段');
  }
};
