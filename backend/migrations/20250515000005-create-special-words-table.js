"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查表是否已存在
    const tables = await queryInterface.showAllTables();

    // 如果表已存在，则跳过创建
    if (!tables.includes("special_words")) {
      // 创建特殊词汇表
      await queryInterface.createTable("special_words", {
        id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
        },
        word: {
          type: Sequelize.STRING(255),
          allowNull: false,
          comment: "特殊词汇",
        },
        service_id: {
          type: Sequelize.STRING(50),
          allowNull: false,
          comment: "TTS服务ID，如google、baidu等",
        },
        language_code: {
          type: Sequelize.STRING(10),
          allowNull: false,
          comment: "语言代码，如ja、zh-CN等",
        },
        is_system: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
          comment: "是否为系统词汇",
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
        },
      });

      // 添加索引
      await queryInterface.addIndex("special_words", ["word", "service_id"], {
        unique: true,
        name: "idx_word_service",
      });
      await queryInterface.addIndex("special_words", ["service_id"], {
        name: "idx_service_id",
      });
      await queryInterface.addIndex("special_words", ["language_code"], {
        name: "idx_language_code",
      });
      await queryInterface.addIndex("special_words", ["is_system"], {
        name: "idx_is_system",
      });
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("special_words");
  },
};
