'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 检查表是否存在
    const tables = await queryInterface.showAllTables();
    
    if (tables.includes('audios')) {
      // 检查字段是否已存在
      const tableDescription = await queryInterface.describeTable('audios');
      
      if (!tableDescription.audio_source) {
        // 添加 audio_source 字段
        await queryInterface.addColumn('audios', 'audio_source', {
          type: Sequelize.ENUM('tts', 'manual', 'upload'),
          allowNull: false,
          defaultValue: 'tts',
          comment: '音频来源：tts-TTS生成，manual-手动切割，upload-直接上传'
        });
        
        // 添加索引
        await queryInterface.addIndex('audios', ['audio_source'], {
          name: 'idx_audio_source'
        });
        
        console.log('已添加 audio_source 字段到 audios 表');
      } else {
        console.log('audio_source 字段已存在，跳过添加');
      }
    } else {
      console.log('audios 表不存在，跳过迁移');
    }
  },

  down: async (queryInterface, Sequelize) => {
    // 检查表是否存在
    const tables = await queryInterface.showAllTables();
    
    if (tables.includes('audios')) {
      // 检查字段是否存在
      const tableDescription = await queryInterface.describeTable('audios');
      
      if (tableDescription.audio_source) {
        // 删除索引
        try {
          await queryInterface.removeIndex('audios', 'idx_audio_source');
        } catch (error) {
          console.log('索引 idx_audio_source 不存在或已删除');
        }
        
        // 删除字段
        await queryInterface.removeColumn('audios', 'audio_source');
        
        console.log('已从 audios 表删除 audio_source 字段');
      } else {
        console.log('audio_source 字段不存在，跳过删除');
      }
    }
  }
};
