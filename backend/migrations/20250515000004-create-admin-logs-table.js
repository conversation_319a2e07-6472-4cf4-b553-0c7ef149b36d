'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查表是否已存在
    const tables = await queryInterface.showAllTables();
    
    // 如果表已存在，则跳过创建
    if (!tables.includes('admin_logs')) {
      // 创建管理员日志表
      await queryInterface.createTable('admin_logs', {
        id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
        },
        admin_id: {
          type: Sequelize.STRING(21),
          allowNull: false,
        },
        admin_email: {
          type: Sequelize.STRING(255),
          allowNull: false,
        },
        action: {
          type: Sequelize.STRING(100),
          allowNull: false,
        },
        target_type: {
          type: Sequelize.STRING(50),
          allowNull: true,
        },
        target_id: {
          type: Sequelize.STRING(50),
          allowNull: true,
        },
        details: {
          type: Sequelize.JSON,
          allowNull: true,
        },
        before_data: {
          type: Sequelize.JSON,
          allowNull: true,
        },
        after_data: {
          type: Sequelize.JSON,
          allowNull: true,
        },
        ip_address: {
          type: Sequelize.STRING(50),
          allowNull: true,
        },
        result: {
          type: Sequelize.ENUM('success', 'failure'),
          defaultValue: 'success',
        },
        message: {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        }
      });

      // 添加索引
      await queryInterface.addIndex('admin_logs', ['admin_id']);
      await queryInterface.addIndex('admin_logs', ['action']);
      await queryInterface.addIndex('admin_logs', ['target_type', 'target_id']);
      await queryInterface.addIndex('admin_logs', ['created_at']);
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('admin_logs');
  }
};
