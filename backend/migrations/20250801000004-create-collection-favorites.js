'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('collection_favorites', {
      id: {
        type: Sequelize.STRING(21),
        primaryKey: true,
        allowNull: false
      },
      collection_id: {
        type: Sequelize.STRING(21),
        allowNull: false,
        comment: '合集ID'
      },
      user_id: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: '用户ID'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 添加外键约束
    await queryInterface.addConstraint('collection_favorites', {
      fields: ['collection_id'],
      type: 'foreign key',
      name: 'fk_collection_favorites_collection_id',
      references: {
        table: 'collections',
        field: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    });

    await queryInterface.addConstraint('collection_favorites', {
      fields: ['user_id'],
      type: 'foreign key',
      name: 'fk_collection_favorites_user_id',
      references: {
        table: 'users',
        field: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    });

    // 添加索引
    await queryInterface.addIndex('collection_favorites', ['collection_id']);
    await queryInterface.addIndex('collection_favorites', ['user_id']);
    
    // 添加唯一约束
    await queryInterface.addConstraint('collection_favorites', {
      fields: ['user_id', 'collection_id'],
      type: 'unique',
      name: 'unique_user_collection'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('collection_favorites');
  }
};
