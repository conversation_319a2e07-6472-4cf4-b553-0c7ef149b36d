/**
 * 添加测试签到记录
 */
const { CheckIn, User } = require('../models');
const { nanoid } = require('nanoid');

async function addTestCheckIns() {
  try {
    // 获取第一个用户
    const user = await User.findOne();
    if (!user) {
      console.log('❌ 没有找到用户，请先创建用户');
      return;
    }

    console.log('👤 使用用户:', user.username);

    // 添加本月的一些签到记录
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    
    console.log(`📅 当前年月: ${year}-${month}`);
    
    const testDates = [
      `${year}-${String(month).padStart(2, '0')}-01`,
      `${year}-${String(month).padStart(2, '0')}-02`,
      `${year}-${String(month).padStart(2, '0')}-03`,
      `${year}-${String(month).padStart(2, '0')}-05`,
      `${year}-${String(month).padStart(2, '0')}-07`,
      `${year}-${String(month).padStart(2, '0')}-10`,
      `${year}-${String(month).padStart(2, '0')}-15`,
    ];

    for (const date of testDates) {
      // 检查是否已存在
      const existing = await CheckIn.findOne({
        where: { userId: user.id, checkInDate: date }
      });

      if (!existing) {
        await CheckIn.create({
          id: nanoid(),
          userId: user.id,
          checkInDate: date,
          rewards: []
        });
        console.log('✅ 添加签到记录:', date);
      } else {
        console.log('⚠️ 签到记录已存在:', date);
      }
    }

    console.log('🎉 测试数据添加完成');
  } catch (error) {
    console.error('❌ 添加测试数据失败:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  addTestCheckIns().then(() => process.exit(0));
}

module.exports = addTestCheckIns;