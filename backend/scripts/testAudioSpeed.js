#!/usr/bin/env node

/**
 * 音频倍速处理测试脚本
 * 用于测试音频倍速处理功能是否正常工作
 */

const audioSpeedService = require('../services/audioSpeedService');

async function testAudioSpeed() {
  try {
    console.log('开始测试音频倍速处理功能...');
    
    // 测试音频路径（使用一个实际存在的音频文件）
    const testAudioPath = 'audio/ja/1749390197378_f5a71f15-68ea-4bdf-9860-8f3ce97bdbb7.mp3';
    const testSpeed = 1.2;
    
    console.log(`测试音频: ${testAudioPath}`);
    console.log(`测试倍速: ${testSpeed}x`);
    
    // 检查缓存
    const hasCached = audioSpeedService.hasCachedFile(testAudioPath, testSpeed);
    console.log(`缓存状态: ${hasCached ? '存在' : '不存在'}`);
    
    if (hasCached) {
      console.log('缓存文件已存在，测试读取缓存...');
      const cacheFile = audioSpeedService.getCacheFilePath(testAudioPath, testSpeed);
      console.log(`缓存文件路径: ${cacheFile}`);
    } else {
      console.log('缓存文件不存在，需要处理音频');
      
      // 测试下载音频
      const originalUrl = `https://echolab.oss-cn-hongkong.aliyuncs.com/${testAudioPath}`;
      console.log(`尝试下载音频: ${originalUrl}`);
      
      try {
        const audioBuffer = await audioSpeedService.downloadAudio(originalUrl);
        console.log(`音频下载成功，大小: ${audioBuffer.length} bytes`);
        
        // 测试倍速处理
        console.log('开始处理音频倍速...');
        const processedBuffer = await audioSpeedService.processAudioSpeed(audioBuffer, testSpeed);
        console.log(`倍速处理完成，处理后大小: ${processedBuffer.length} bytes`);
        
        // 保存缓存
        await audioSpeedService.saveCachedFile(testAudioPath, testSpeed, processedBuffer);
        console.log('缓存保存成功');
        
      } catch (error) {
        console.error('音频处理失败:', error.message);
      }
    }
    
    console.log('测试完成');
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testAudioSpeed().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('测试异常:', error);
    process.exit(1);
  });
}

module.exports = testAudioSpeed;
