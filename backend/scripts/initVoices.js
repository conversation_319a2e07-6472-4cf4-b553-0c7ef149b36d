/**
 * 初始化声音信息数据
 * 将基本的声音信息填充到数据库中
 */
const db = require("../models");

// 声音信息数据
const voicesData = [
  // Google TTS声音 - 日语
  {
    speaker_id: "ja-JP-Wavenet-A",
    service_id: "google",
    language_code: "ja",
    name: "女声A",
    gender: "female",
    is_premium: false,
    disabled: false,
    api_params: { language_code: "ja-JP" },
  },
  {
    speaker_id: "ja-JP-Wavenet-B",
    service_id: "google",
    language_code: "ja",
    name: "男声A",
    gender: "male",
    is_premium: false,
    disabled: false,
    api_params: { language_code: "ja-<PERSON>" },
  },
  {
    speaker_id: "ja-JP-Wavenet-C",
    service_id: "google",
    language_code: "ja",
    name: "男声B",
    gender: "male",
    is_premium: false,
    disabled: false,
    api_params: { language_code: "ja-<PERSON>" },
  },
  {
    speaker_id: "ja-JP-Wavenet-D",
    service_id: "google",
    language_code: "ja",
    name: "女声B",
    gender: "female",
    is_premium: false,
    disabled: false,
    api_params: { language_code: "ja-JP" },
  },
  {
    speaker_id: "ja-JP-Neural2-B",
    service_id: "google",
    language_code: "ja",
    name: "高级男声",
    gender: "male",
    is_premium: true,
    disabled: false,
    api_params: { language_code: "ja-JP" },
  },
  {
    speaker_id: "ja-JP-Neural2-C",
    service_id: "google",
    language_code: "ja",
    name: "高级女声",
    gender: "female",
    is_premium: true,
    disabled: false,
    api_params: { language_code: "ja-JP" },
  },

  // Google TTS声音 - 英语
  {
    speaker_id: "en-US-Wavenet-A",
    service_id: "google",
    language_code: "en",
    name: "女声A",
    gender: "female",
    is_premium: false,
    disabled: false,
    api_params: { language_code: "en-US" },
  },
  {
    speaker_id: "en-US-Wavenet-B",
    service_id: "google",
    language_code: "en",
    name: "男声A",
    gender: "male",
    is_premium: false,
    disabled: false,
    api_params: { language_code: "en-US" },
  },
  {
    speaker_id: "en-US-Wavenet-C",
    service_id: "google",
    language_code: "en",
    name: "女声B",
    gender: "female",
    is_premium: false,
    disabled: false,
    api_params: { language_code: "en-US" },
  },
  {
    speaker_id: "en-US-Wavenet-D",
    service_id: "google",
    language_code: "en",
    name: "男声B",
    gender: "male",
    is_premium: false,
    disabled: false,
    api_params: { language_code: "en-US" },
  },
  {
    speaker_id: "en-US-Neural2-F",
    service_id: "google",
    language_code: "en",
    name: "高级女声",
    gender: "female",
    is_premium: true,
    disabled: false,
    api_params: { language_code: "en-US" },
  },
  {
    speaker_id: "en-US-Neural2-J",
    service_id: "google",
    language_code: "en",
    name: "高级男声",
    gender: "male",
    is_premium: true,
    disabled: false,
    api_params: { language_code: "en-US" },
  },

  // Google TTS声音 - 简体中文
  {
    speaker_id: "cmn-CN-Wavenet-A",
    service_id: "google",
    language_code: "zh-CN",
    name: "女声A",
    gender: "female",
    is_premium: true,
    disabled: false,
    api_params: { languageCode: "cmn-CN" },
  },
  {
    speaker_id: "cmn-CN-Wavenet-B",
    service_id: "google",
    language_code: "zh-CN",
    name: "男声A",
    gender: "male",
    is_premium: true,
    disabled: false,
    api_params: { languageCode: "cmn-CN" },
  },
  {
    speaker_id: "cmn-CN-Wavenet-C",
    service_id: "google",
    language_code: "zh-CN",
    name: "男声B",
    gender: "male",
    is_premium: true,
    disabled: false,
    api_params: { languageCode: "cmn-CN" },
  },
  {
    speaker_id: "cmn-CN-Wavenet-D",
    service_id: "google",
    language_code: "zh-CN",
    name: "女声B",
    gender: "female",
    is_premium: true,
    disabled: false,
    api_params: { languageCode: "cmn-CN" },
  },

  // Google TTS声音 - 繁体中文（台湾）
  {
    speaker_id: "cmn-TW-Wavenet-A",
    service_id: "google",
    language_code: "zh-TW",
    name: "女声A",
    gender: "female",
    is_premium: true,
    disabled: false,
    api_params: { languageCode: "cmn-TW" },
  },
  {
    speaker_id: "cmn-TW-Wavenet-B",
    service_id: "google",
    language_code: "zh-TW",
    name: "男声A",
    gender: "male",
    is_premium: true,
    disabled: false,
    api_params: { languageCode: "cmn-TW" },
  },
  {
    speaker_id: "cmn-TW-Wavenet-C",
    service_id: "google",
    language_code: "zh-TW",
    name: "男声B",
    gender: "male",
    is_premium: true,
    disabled: false,
    api_params: { languageCode: "cmn-TW" },
  },
];

// 服务信息数据
const servicesData = [
  {
    id: "google",
    name: "Google TTS",
    description: "Google Cloud Text-to-Speech服务",
  },
  {
    id: "baidu",
    name: "百度TTS",
    description: "百度语音合成服务",
  },
];

// 默认映射关系
const defaultMappings = {
  ja: {
    default: "ja-JP-Wavenet-A",
    A: "ja-JP-Wavenet-A",
    B: "ja-JP-Wavenet-C",
    C: "ja-JP-Wavenet-D",
  },
  "zh-CN": {
    default: "cmn-CN-Wavenet-A", // 女声A
    A: "cmn-CN-Wavenet-A", // 女声A
    B: "cmn-CN-Wavenet-B", // 男声A
    C: "cmn-CN-Wavenet-D", // 女声B
  },
  "zh-TW": {
    default: "cmn-TW-Wavenet-A", // 女声A
    A: "cmn-TW-Wavenet-A", // 女声A
    B: "cmn-TW-Wavenet-B", // 男声A
    C: "cmn-TW-Wavenet-C", // 男声B
  },
  en: {
    default: "en-US-Wavenet-A",
    A: "en-US-Wavenet-A",
    B: "en-US-Wavenet-B",
    C: "en-US-Wavenet-C",
  },
};

/**
 * 初始化声音信息
 */
async function initVoices() {
  try {
    console.log("开始初始化声音信息...");

    // 检查voices表是否存在
    try {
      // 尝试查询voices表
      await db.sequelize.query("SELECT 1 FROM voices LIMIT 1");

      // 如果表存在，清空现有数据
      await db.Voice.destroy({ where: {} });
      console.log("已清空现有声音信息数据");
    } catch (error) {
      // 如果表不存在，创建表
      if (error.original && error.original.code === "ER_NO_SUCH_TABLE") {
        console.log("voices表不存在，将通过模型创建");
        await db.sequelize.query(`
          CREATE TABLE IF NOT EXISTS voices (
            id INT AUTO_INCREMENT PRIMARY KEY,
            speaker_id VARCHAR(50) NOT NULL,
            service_id VARCHAR(20) NOT NULL,
            language_code VARCHAR(10) NOT NULL,
            name VARCHAR(50) NOT NULL,
            gender ENUM('male', 'female', 'neutral') NOT NULL,
            is_premium BOOLEAN NOT NULL DEFAULT FALSE,
            disabled BOOLEAN NOT NULL DEFAULT FALSE,
            api_params JSON,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_speaker_id (speaker_id),
            INDEX idx_service_id (service_id),
            INDEX idx_language_code (language_code)
          )
        `);
        console.log("voices表创建成功");
      } else {
        throw error;
      }
    }

    // 批量创建声音信息
    await db.Voice.bulkCreate(voicesData);
    console.log(`已创建${voicesData.length}条声音信息数据`);

    console.log("声音信息初始化完成");
    return true;
  } catch (error) {
    console.error("初始化声音信息失败:", error);
    return false;
  }
}

// 如果直接执行此脚本，则初始化声音信息
if (require.main === module) {
  (async () => {
    try {
      // 不再尝试重建所有表，只操作voices表
      console.log("开始初始化声音信息...");

      // 初始化声音信息
      await initVoices();
    } catch (error) {
      console.error("执行脚本失败:", error);
    } finally {
      // 关闭数据库连接
      await db.sequelize.close();
    }
  })();
}

// 导出函数
module.exports = initVoices;
