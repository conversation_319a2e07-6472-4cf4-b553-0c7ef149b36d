/**
 * 将 px 单位转换为 rem 单位的脚本
 * 使用方法：node convert-px-to-rem.js <文件路径>
 */

const fs = require("fs");
const path = require("path");

// 转换比例：1px = 1/16rem = 0.0625rem

// 转换函数
function pxToRem(px) {
  const value = parseFloat(px);
  if (isNaN(value)) return px;
  // 直接计算 px/16，保留4位小数，提供更精准的转换
  const rem = (value / 16).toFixed(4).replace(/\.?0+$/, "");
  return `${rem}rem`;
}

// 不使用预定义的转换规则，直接计算所有像素值

// 正则表达式匹配 CSS 中的 px 值
const pxRegex = /(\d*\.?\d+)px/g;

// 处理单个文件
function processFile(filePath) {
  console.log(`处理文件: ${filePath}`);

  // 读取文件内容
  let content = fs.readFileSync(filePath, "utf8");

  // 记录原始内容，用于比较是否有变化
  const originalContent = content;

  // 替换所有 px 值
  content = content.replace(pxRegex, (_match, value) => {
    const rem = pxToRem(value);
    return rem;
  });

  // 如果内容有变化，写入文件
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, "utf8");
    console.log(`已更新文件: ${filePath}`);
  } else {
    console.log(`文件无变化: ${filePath}`);
  }
}

// 处理目录
function processDirectory(dirPath) {
  console.log(`处理目录: ${dirPath}`);

  // 读取目录内容
  const items = fs.readdirSync(dirPath);

  // 遍历目录内容
  for (const item of items) {
    const itemPath = path.join(dirPath, item);
    const stats = fs.statSync(itemPath);

    // 如果是目录，递归处理
    if (stats.isDirectory()) {
      // 排除 node_modules 和 .git 目录
      if (item !== "node_modules" && item !== ".git") {
        processDirectory(itemPath);
      }
    }
    // 如果是文件，且是 Vue 或 CSS 文件，处理文件
    else if (
      stats.isFile() &&
      (item.endsWith(".vue") || item.endsWith(".css"))
    ) {
      processFile(itemPath);
    }
  }
}

// 主函数
function main() {
  const args = process.argv.slice(2);

  if (args.length === 0) {
    console.log("请提供文件或目录路径");
    console.log("使用方法: node convert-px-to-rem.js <文件路径或目录路径>");
    return;
  }

  const targetPath = args[0];

  // 检查路径是否存在
  if (!fs.existsSync(targetPath)) {
    console.log(`路径不存在: ${targetPath}`);
    return;
  }

  // 检查是文件还是目录
  const stats = fs.statSync(targetPath);

  if (stats.isFile()) {
    // 如果是文件，处理单个文件
    processFile(targetPath);
  } else if (stats.isDirectory()) {
    // 如果是目录，处理整个目录
    processDirectory(targetPath);
  }
}

// 执行主函数
main();
