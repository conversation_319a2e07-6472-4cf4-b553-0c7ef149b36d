/**
 * 更新用户等级表数据
 *
 * 使用方法：
 * 1. 将此文件放在项目根目录
 * 2. 设置环境变量 NODE_ENV=production
 * 3. 执行 node update-user-levels.js
 */

// 设置环境变量
process.env.NODE_ENV = process.env.NODE_ENV || "production";

// 导入数据库配置
const path = require("path");
const fs = require("fs");

// 检查配置文件是否存在
const configPath = path.join(__dirname, "backend", "config", "config.json");
if (!fs.existsSync(configPath)) {
  console.error(`错误: 找不到配置文件 ${configPath}`);
  process.exit(1);
}

// 读取配置
const config = require(configPath);
const env = process.env.NODE_ENV || "development";
const dbConfig = config[env];

console.log(`使用 ${env} 环境的数据库配置`);
console.log(`数据库: ${dbConfig.database}`);
console.log(`用户名: ${dbConfig.username}`);
console.log(`主机: ${dbConfig.host}`);

// 导入数据库模型
const db = require("./backend/models");

// 用户等级数据
const userLevels = [
  {
    level: 0,
    name: "免费用户",
    description: "基本功能访问",
    isDefault: true,
  },
  {
    level: 1,
    name: "基础会员",
    description: "解锁更多功能",
    isDefault: false,
  },
  {
    level: 2,
    name: "高级会员",
    description: "全部功能访问",
    isDefault: false,
  },
];

/**
 * 更新用户等级表
 */
async function updateUserLevels() {
  try {
    console.log("开始更新用户等级表...");

    // 查询现有记录
    const existingLevels = await db.UserLevel.findAll();
    console.log(`找到 ${existingLevels.length} 条现有记录`);

    // 更新每个等级
    for (const levelData of userLevels) {
      const [level, created] = await db.UserLevel.findOrCreate({
        where: { level: levelData.level },
        defaults: levelData,
      });

      if (!created) {
        // 如果记录已存在但名称或描述为空，则更新
        if (!level.name || !level.description) {
          await level.update({
            name: levelData.name,
            description: levelData.description,
            isDefault: levelData.isDefault,
          });
          console.log(`已更新等级 ${levelData.level} (${levelData.name})`);
        } else {
          console.log(
            `等级 ${levelData.level} (${level.name}) 已存在且有数据，跳过更新`
          );
        }
      } else {
        console.log(`已创建新等级 ${levelData.level} (${levelData.name})`);
      }
    }

    // 再次查询确认更新结果
    const updatedLevels = await db.UserLevel.findAll({
      order: [["level", "ASC"]],
    });

    console.log("\n更新后的用户等级表数据:");
    updatedLevels.forEach((level) => {
      console.log(
        `ID: ${level.id}, 等级: ${level.level}, 名称: ${level.name}, 描述: ${level.description}, 默认: ${level.isDefault}`
      );
    });

    console.log("\n用户等级表更新完成!");
  } catch (error) {
    console.error("更新用户等级表失败:", error);
  } finally {
    // 关闭数据库连接
    await db.sequelize.close();
  }
}

/**
 * 直接使用 MySQL 连接更新用户等级表
 * 如果 Sequelize 连接失败，可以使用此函数
 */
async function updateUserLevelsDirectSQL() {
  // 检查是否安装了 mysql2
  try {
    require("mysql2/promise");
  } catch (error) {
    console.error("缺少 mysql2 模块，请先安装: npm install mysql2");
    return false;
  }

  const mysql = require("mysql2/promise");

  // 创建数据库连接
  console.log("尝试直接使用 MySQL 连接...");

  // 提示用户输入数据库凭据
  const readline = require("readline").createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  const question = (query) =>
    new Promise((resolve) => readline.question(query, resolve));

  try {
    const host =
      (await question("数据库主机 (默认: localhost): ")) || "localhost";
    const user = await question("数据库用户名: ");
    const password = await question("数据库密码: ");
    const database = await question("数据库名称: ");

    // 创建连接
    const connection = await mysql.createConnection({
      host,
      user,
      password,
      database,
    });

    console.log("数据库连接成功!");

    // 查询现有记录
    const [rows] = await connection.execute("SELECT * FROM user_levels");
    console.log(`找到 ${rows.length} 条现有记录`);

    // 更新每个等级
    for (const levelData of userLevels) {
      // 检查记录是否存在
      const [existingRows] = await connection.execute(
        "SELECT * FROM user_levels WHERE level = ?",
        [levelData.level]
      );

      if (existingRows.length === 0) {
        // 创建新记录
        await connection.execute(
          "INSERT INTO user_levels (level, name, description, is_default, created_at, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW())",
          [
            levelData.level,
            levelData.name,
            levelData.description,
            levelData.isDefault ? 1 : 0,
          ]
        );
        console.log(`已创建新等级 ${levelData.level} (${levelData.name})`);
      } else {
        // 更新现有记录
        const row = existingRows[0];
        if (!row.name || !row.description) {
          await connection.execute(
            "UPDATE user_levels SET name = ?, description = ?, is_default = ?, updated_at = NOW() WHERE level = ?",
            [
              levelData.name,
              levelData.description,
              levelData.isDefault ? 1 : 0,
              levelData.level,
            ]
          );
          console.log(`已更新等级 ${levelData.level} (${levelData.name})`);
        } else {
          console.log(
            `等级 ${levelData.level} (${row.name}) 已存在且有数据，跳过更新`
          );
        }
      }
    }

    // 查询更新后的记录
    const [updatedRows] = await connection.execute(
      "SELECT * FROM user_levels ORDER BY level ASC"
    );

    console.log("\n更新后的用户等级表数据:");
    updatedRows.forEach((row) => {
      console.log(
        `ID: ${row.id}, 等级: ${row.level}, 名称: ${row.name}, 描述: ${row.description}, 默认: ${row.is_default}`
      );
    });

    // 关闭连接
    await connection.end();
    readline.close();

    console.log("\n用户等级表更新完成!");
    return true;
  } catch (error) {
    console.error("直接 MySQL 连接更新失败:", error);
    readline.close();
    return false;
  }
}

// 执行更新
async function main() {
  try {
    // 先尝试使用 Sequelize
    await updateUserLevels();
  } catch (error) {
    console.error("使用 Sequelize 更新失败:", error);
    console.log("\n尝试使用直接 MySQL 连接...");

    // 如果 Sequelize 失败，尝试直接 MySQL 连接
    await updateUserLevelsDirectSQL();
  }
}

main();
